﻿// TestTypeListController
adminTestTypeApp.controller('TestTypeListController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTestTypeWebAPIService', '$uibModal',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTestTypeWebAPIService, $uibModal) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Test Types | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $rootScope.title = "Test Types | Proprietor";
            }
            $rootScope.PageHeading = "Test Types";
            $rootScope.SubPageHeading = null;

            $scope.sortKey = "percentage";
            $scope.reverse = false;

            loadTestTypes(); // get content
        }

        function loadTestTypes() {
            $scope.contentLoading = true; // show loading icon

            var promiseGetTestTypes = AdminTestTypeWebAPIService.getTestTypes();
            promiseGetTestTypes.then(function (pl) {
                $scope.TestTypes = pl.data;
            }, function (errorPl) {
                SharedMethods.createErrorToast("Problem loading information, please reload the page");
            })
            .finally(function () {
                $scope.contentLoading = false;
            });
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        }

        $scope.search = function (row) {
            return ((row.name != null && angular.lowercase(row.name).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.percentage != null && angular.lowercase(row.percentage + "").indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.description != null && angular.lowercase(row.description).indexOf(angular.lowercase($scope.searchText) || '') !== -1))
        }

        $scope.reload = function () {
            $scope.searchText = "";
            loadTestTypes();
        }

        $scope.deleteTestType = function (id, name) {
            var ModalModel = {
                Id : id,
                HeaderContent: "Delete " + name,
                BodyMessage: "Warning!! All results entered for this test type will be deleted permanently along with the test type."
                + " Please be advised, this action is not reversible.",
                ButtonText: "Delete",
                ButtonClass: "btn-danger"
            }

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'TestTypeDeleteController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

    }]);

// TestTypeInsertController
adminTestTypeApp.controller('TestTypeInsertController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTestTypeWebAPIService', 'PercentageAssigned',
    '$state',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTestTypeWebAPIService, PercentageAssigned, $state) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Add Test Type | Super Admin";
            if (window.location.href.indexOf("/proprietor/") !== -1) {
                $rootScope.title = "Add Test Type | Proprietor";
            }
            $rootScope.PageHeading = "Add Test Type";
            $rootScope.SubPageHeading = null;

            $scope.PercentageAssigned = PercentageAssigned;
            $scope.ButtonText = "Create";
            $scope.TestType = {};
            $scope.Temp = {
                percentage: 0
            };
            if (Number.parseInt($scope.PercentageAssigned) === 100) {
                $scope.Complete = true;
                SharedMethods.createErrorToast("Total percentage of 100 has been completely assigned.", false);
            }
        }

        $rootScope.getParentUrl = function () {
            return $state.href('testtype');
        };

        $scope.deleteKeytriggered = false;
        $scope.keyTriggered = false;
        $scope.percentageChanged = function () {
            if ($scope.TestType.percentage === undefined && !$scope.deleteKeytriggered && $scope.keyTriggered)
                $scope.TestType.percentage = 100 - $scope.PercentageAssigned + $scope.Temp.percentage;
        };
        $scope.detectBackDown = function (event) {
            $scope.keyTriggered = true;
            if (event.keyCode === 8 || event.keyCode === 46) {
                $scope.deleteKeytriggered = true;
            }
            else $scope.deleteKeytriggered = false;
        };
        $scope.percentageBlur = function () {
            $scope.keyTriggered = false;
        };

        $scope.save = function () {
            if (!$scope.Complete) {
                var submitBtn = Ladda.create(document.querySelector('#button-createTestType'));
                submitBtn.start();
                $scope.processing = true;
                $scope.fieldErrors = {};

                var promiseSubmitTestType = AdminTestTypeWebAPIService.createTestType($scope.TestType);
                promiseSubmitTestType.then(function (pl) {
                    //redirect to list
                    $state.go('testtype');
                    //show success message
                    SharedMethods.createSuccessToast("<strong>Test Type</strong> was created successfully!");
                },
                function (error) {
                    $scope.processing = false;
                    submitBtn.stop();
                    SharedMethods.showValidationErrors($scope, error);
                });
            }
        }
    }]);

// TestTypeEditController
adminTestTypeApp.controller('TestTypeEditController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTestTypeWebAPIService', 'TestTypeInfo', '$state',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTestTypeWebAPIService, TestTypeInfo, $state) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Edit Test Type | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $rootScope.title = "Edit Test Type | Proprietor";
            }
            $rootScope.PageHeading = "Edit Test Type";
            $rootScope.SubPageHeading = null;

            $scope.TestType = TestTypeInfo.testType;
            $scope.PercentageAssigned = TestTypeInfo.percentageAssigned;
            $scope.Temp = {
                percentage: $scope.TestType.percentage
            };
            $scope.ButtonText = "Save";
        }

        $rootScope.getParentUrl = function () {
            return $state.href('testtype');
        };

        $scope.deleteKeytriggered = false;
        $scope.keyTriggered = false;
        $scope.percentageChanged = function () {
            if ($scope.TestType.percentage === undefined && !$scope.deleteKeytriggered && $scope.keyTriggered)
                $scope.TestType.percentage = 100 - $scope.PercentageAssigned + $scope.Temp.percentage;
        }
        $scope.detectBackDown = function (event) {
            $scope.keyTriggered = true;
            if (event.keyCode === 8 || event.keyCode === 46) {
                $scope.deleteKeytriggered = true;
            }
            else $scope.deleteKeytriggered = false;
        };
        $scope.percentageBlur = function () {
            $scope.keyTriggered = false;
        }

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-createTestType'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var promisePutTestType = AdminTestTypeWebAPIService.putTestType($scope.TestType.id, $scope.TestType);
            promisePutTestType.then(function (pl) {
                //redirect to list
                $state.go('testtype');
                //show success message
                SharedMethods.createSuccessToast("<strong>Test Type</strong> information was updated successfully!");
            },
            function (error) {
                $scope.processing = false;
                submitBtn.stop();
                SharedMethods.showValidationErrors($scope, error);
            });
        }
    }]);

// TestTypeDeleteController
adminTestTypeApp.controller('TestTypeDeleteController', ['$scope', '$uibModalInstance', 'AdminTestTypeWebAPIService', '$state', 'ModalModel', 'SharedMethods',
    function ($scope, $uibModalInstance, AdminTestTypeWebAPIService, $state, ModalModel, SharedMethods) {

        $scope.ModalModel = ModalModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);
            var promiseDelete = AdminTestTypeWebAPIService.deleteTestType($scope.ModalModel.Id);

            promiseDelete.then(function (pl) {
                //dismiss modal
                $uibModalInstance.close();
                //redirect to List
                $state.go('testtype', {}, { reload: true });
                //show success message
                SharedMethods.createSuccessToast('<strong>Test Type</strong> was deleted successfully!');
            },
            function (error) {
                //dismiss modal
                $uibModalInstance.close();
                //show error message
                SharedMethods.createErrorToast('Problem deleting <strong>Test Type</strong>. Try again!');
            });
        }
    }
  ]);

// Error404Controller
adminTestTypeApp.controller('Error404Controller', ['$scope', '$rootScope',
    function ($scope, $rootScope) {
        $rootScope.title = "Page Not Found | School Cater";
        $rootScope.activePage = "";
        $rootScope.removeHeadSection = true;
    }]);