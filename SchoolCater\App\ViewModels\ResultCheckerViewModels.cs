﻿using Newtonsoft.Json;
using System.ComponentModel.DataAnnotations;

namespace SchoolCater.App.ViewModels
{
    public class ResultCheckerSession
    {
        public int Id { get; set; }
        public string Name { get; set; }
    }

    public class ResultCheckerSchool : ResultCheckerSession
    {
        [JsonIgnore]
        public int? GroupId { get; set; }
    }

    public class ResultCheckerTerm : ResultCheckerSession
    {
    }

    public class ResultCheckerNoScratchCard
    {
        [Required(ErrorMessage = "Please enter your student ID.")]
        public string StudentId { get; set; }
        [Required(ErrorMessage = "Please select your school.")]
        public int School { get; set; }
    }

    public class ResultChecker: ResultCheckerNoScratchCard 
    {
        [Required(ErrorMessage = "Please select a session.")]
        public int Session { get; set; }
        [Required(ErrorMessage = "Please select a term.")]
        public int Term { get; set; }
        [Required]
        public string Pin { get; set; }
        [Required]
        public string Serial { get; set; }
    }

    public class CompleteProfileResultChecker: ResultChecker
    {
        // student info
        [Required(ErrorMessage = "Please enter the last name of the student.")]
        public string StudentLastName { get; set; }
        [Required(ErrorMessage = "Please enter the first name of the student.")]
        public string StudentFirstName { get; set; }
        public string StudentMiddleName { get; set; }
        [Required(ErrorMessage = "Please select the student's date of birth.")]
        public string StudentDOB { get; set; }

        // parent info
        [Required(ErrorMessage = "Please enter the title of the parent.")]
        public string ParentTitle { get; set; }
        [Required(ErrorMessage = "Please enter the last name of the parent.")]
        public string ParentLastName { get; set; }
        [Required(ErrorMessage = "Please enter the first name of the parent.")]
        public string ParentFirstName { get; set; }
        [Required(ErrorMessage = "Please enter the gender of the parent.")]
        public string ParentGender { get; set; }
        [Required(ErrorMessage = "Please enter the phone number of the parent.")]
        public string ParentPhone { get; set; }
    }

    public class DisplayResultChecker
    {
        [Required(ErrorMessage = "Please select a session.")]
        public int session { get; set; }
        [Required(ErrorMessage = "Please select a term.")]
        public int term { get; set; }
        public string token { get; set; }
    }

    public class ResultPDF
    {
        public string Token { get; set; }
        public string CssLinks { get; set; }
        public string Html { get; set; }
    }
}