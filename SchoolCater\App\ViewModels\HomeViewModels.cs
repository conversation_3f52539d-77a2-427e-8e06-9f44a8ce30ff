﻿using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace SchoolCater.App.ViewModels
{
    public class InquiryViewModel
    {
        [Required(ErrorMessage = "Please enter your name.")]
        public string Name { get; set; }
        [Required(ErrorMessage = "Please enter your email address.")]
        [EmailAddress(ErrorMessage = "Please enter a valid email address.")]
        public string Email { get; set; }
        [Required(ErrorMessage = "Please enter your message.")]
        public string Message { get; set; }
    }

    public class RecommendationViewModel
    {
        [Required(ErrorMessage = "Please enter your name.")]
        public string Sender { get; set; }
        public string SenderType { get; set; }
        [Required(ErrorMessage = "Please enter the contact person's name.")]
        public string RecipientName { get; set; }
        [Required(ErrorMessage = "Please enter the contact person's phone number.")]
        [Phone]
        public string RecipientPhone { get; set; }
        [EmailAddress]
        public string RecipientEmail { get; set; }
    }

    public class CustomerGroupViewModel
    {
        [JsonIgnore]
        public int Id { get; set; }
        [JsonIgnore]
        public string Name { get; set; }
        public byte[] Logo { get; set; }
    }

    public class CustomerViewModel: CustomerGroupViewModel
    {
        [JsonIgnore]
        public int? GroupId { get; set; }
        public string LogoType { get; set; }
        [JsonIgnore]
        public CustomerGroupViewModel GroupSchool { get; set; }
    }
}