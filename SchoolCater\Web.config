<?xml version="1.0"?>
<!--
  For more information on how to configure your ASP.NET application, please visit
  https://go.microsoft.com/fwlink/?LinkId=301880
  -->
<configuration>
  <configSections>
    <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 -->
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false"/>
    <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net"/>
  </configSections>
  <connectionStrings>
    <!--<add name="DefaultConnection" connectionString="Data Source=(LocalDb)\v11.0;AttachDbFilename=|DataDirectory|\aspnet-SchoolCater-20160728011711.mdf;Initial Catalog=aspnet-SchoolCater-20160728011711;Integrated Security=True" providerName="System.Data.SqlClient" />-->
	 <!-- <add name="SchoolEntities" connectionString="metadata=res://*/Models.SchoolSolutionModel.csdl|res://*/Models.SchoolSolutionModel.ssdl|res://*/Models.SchoolSolutionModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=EBOOK-PC\SQLEXPRESS;initial catalog=Online_SchoolSolution;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
   <!-- <add name="SchoolEntities" connectionString="metadata=res://*/Models.SchoolSolutionModel.csdl|res://*/Models.SchoolSolutionModel.ssdl|res://*/Models.SchoolSolutionModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=AGOZIE-PC\SQLEXPRESS;initial catalog=Schoolcater;persist security info=True;user id=sa;password=password;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/> -->
    <add name="SchoolEntities" connectionString="metadata=res://*/Models.SchoolSolutionModel.csdl|res://*/Models.SchoolSolutionModel.ssdl|res://*/Models.SchoolSolutionModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=s14.winhost.com;initial catalog=DB_123282_schoolcater;persist security info=True;user id=DB_123282_schoolcater_user;password=**********;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient"/>
    <!--<add name="SchoolEntities" connectionString="metadata=res://*/Models.SchoolSolutionModel.csdl|res://*/Models.SchoolSolutionModel.ssdl|res://*/Models.SchoolSolutionModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-B6D5IE3;initial catalog=SchoolSolution;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
    <!--<add name="SchoolEntities" connectionString="metadata=res://*/Models.SchoolSolutionModel.csdl|res://*/Models.SchoolSolutionModel.ssdl|res://*/Models.SchoolSolutionModel.msl;provider=System.Data.SqlClient;provider connection string=&quot;data source=DESKTOP-B6D5IE3;initial catalog=SchoolCaterOnline;integrated security=True;MultipleActiveResultSets=True;App=EntityFramework&quot;" providerName="System.Data.EntityClient" />-->
  </connectionStrings>
  <appSettings>
    <add key="webpages:Version" value="*******"/>
    <add key="webpages:Enabled" value="false"/>
    <add key="ClientValidationEnabled" value="true"/>
    <add key="UnobtrusiveJavaScriptEnabled" value="true"/>
    <!-- Send Grid-->
    <add key="mailAccount" value="ebuka1"/>
    <add key="mailPassword" value="OghuanSegun123"/>
    <add key="SENDGRID_APIKEY" value="*********************************************************************"/>
    <!-- Flutterwave -->
    <add key="FLUTTER_TEST_KEY" value="FLWSECK_TEST-60c4b51355ce211392c3d4e7bb0bd0a7-X"/>
    <add key="FLUTTER_LIVE_KEY" value="******************************************"/>
    <!--Smptp Server -->
    <add key="GmailUserName" value="<EMAIL>"/>
    <add key="GmailPassword" value="OghuanSegun1."/>
    <add key="GmailHost" value="smtp.gmail.com"/>
    <add key="GmailPort" value="587"/>
    <add key="GmailSsl" value="true"/>

    <!--Smptp Server -->
    <add key="WinhostEmail" value="<EMAIL>"/>
    <add key="WinhostPassword" value="SchoolSolution101."/>
    <add key="WinhostServer" value="m05.internetmailserver.net"/>
    <add key="WinhostPort" value="587"/>
    <add key="WinhostSsl" value="true"/>
  </appSettings>
  <!--
    For a description of web.config changes see http://go.microsoft.com/fwlink/?LinkId=235367.

    The following attributes can be set on the <httpRuntime> tag.
      <system.Web>
        <httpRuntime targetFramework="4.6.1" />
      </system.Web>
  -->
  <system.web>
    <compilation debug="true" targetFramework="4.5.2"/>
    <httpRuntime targetFramework="4.5.2"/>
    <httpModules>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web"/>
    </httpModules>
    <customErrors mode="RemoteOnly" redirectMode="ResponseRedirect" defaultRedirect="~/                                                 /Error/404.aspx">
      <error statusCode="404" redirect="~/                              /Error/404.aspx"/>
    </customErrors>
    <trust level="Full"/>
  </system.web>
  <log4net>
    <root>
      <level value="INFO"/>
      <appender-ref ref="RollingFileAppender"/>
    </root>
    <appender name="RollingFileAppender" type="log4net.Appender.RollingFileAppender">
      <file value="Log/SchoolCaterLogFile.txt"/>
      <appendToFile value="true"/>
      <rollingStyle value="Size"/>
      <maxSizeRollBackups value="5"/>
      <maximumFileSize value="10MB"/>
      <staticLogFileName value="true"/>
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %level %logger - %message%newline"/>
      </layout>
    </appender>
  </log4net>
  <system.webServer>
    <validation validateIntegratedModeConfiguration="false"/>
    <modules>
      <remove name="ApplicationInsightsWebTracking"/>
      <add name="ApplicationInsightsWebTracking" type="Microsoft.ApplicationInsights.Web.ApplicationInsightsHttpModule, Microsoft.AI.Web" preCondition="managedHandler"/>
    </modules>
    <handlers>
      <remove name="ExtensionlessUrlHandler-Integrated-4.0"/>
      <remove name="OPTIONSVerbHandler"/>
      <remove name="TRACEVerbHandler"/>
      <add name="ExtensionlessUrlHandler-Integrated-4.0" path="*." verb="*" type="System.Web.Handlers.TransferRequestHandler" preCondition="integratedMode,runtimeVersionv4.0"/>
    </handlers>
  </system.webServer>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" culture="neutral" publicKeyToken="30ad4fe6b2a6aeed"/>
        <bindingRedirect oldVersion="0.0.0.0-9.0.0.0" newVersion="9.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Optimization" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-1.1.0.0" newVersion="1.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="WebGrease" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="0.0.0.0-1.5.2.14234" newVersion="1.5.2.14234"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Helpers" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Mvc" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.WebPages" publicKeyToken="31bf3856ad364e35"/>
        <bindingRedirect oldVersion="1.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.6.0" newVersion="5.2.6.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.6.0" newVersion="5.2.6.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.AspNet.Identity.Core" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.0.0.0" newVersion="2.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-*******" newVersion="*******"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
  <system.codedom>
    <compilers>
      <compiler language="c#;cs;csharp" extension=".cs" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.CSharpCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:6 /nowarn:1659;1699;1701"/>
      <compiler language="vb;vbs;visualbasic;vbscript" extension=".vb" type="Microsoft.CodeDom.Providers.DotNetCompilerPlatform.VBCodeProvider, Microsoft.CodeDom.Providers.DotNetCompilerPlatform, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35" warningLevel="4" compilerOptions="/langversion:14 /nowarn:41008 /define:_MYTYPE=\&quot;Web\&quot; /optionInfer+"/>
    </compilers>
  </system.codedom>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.SqlConnectionFactory, EntityFramework"/>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
</configuration>