<!-- Student Quiz Token Input Modal -->
<div class="modal fade" id="quizTokenInputModal" tabindex="-1" role="dialog" aria-labelledby="quizTokenInputModalLabel">
    <div class="modal-dialog" role="document">
        <div class="modal-content">
            <div class="modal-header">
                <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                    <span aria-hidden="true">&times;</span>
                </button>
                <h4 class="modal-title" id="quizTokenInputModalLabel">
                    <i class="fa fa-key"></i> Enter Quiz Access Token
                </h4>
            </div>
            <div class="modal-body">
                <div class="text-center" style="margin-bottom: 20px;">
                    <i class="fa fa-shield fa-3x text-primary"></i>
                    <h4 style="margin-top: 15px;">Secure Quiz Access</h4>
                    <p class="text-muted">This quiz requires a 6-digit access token provided by your supervisor.</p>
                </div>

                <!-- Quiz Information -->
                <div class="alert alert-info">
                    <strong>Quiz:</strong> {{tokenInput.quiz.title}}<br>
                    <strong>Subject:</strong> {{tokenInput.subject.name}}<br>
                    <strong>Duration:</strong> {{tokenInput.quiz.duration}} minutes
                </div>

                <!-- Token Input Form -->
                <form name="tokenForm" ng-submit="submitToken()" novalidate>
                    <div class="form-group">
                        <label for="tokenInput" class="control-label">
                            <i class="fa fa-key"></i> Access Token
                        </label>
                        <input type="text" 
                               class="form-control text-center" 
                               id="tokenInput"
                               name="token"
                               ng-model="tokenInput.token" 
                               placeholder="Enter 6-digit token"
                               maxlength="6"
                               pattern="[0-9]{6}"
                               required
                               style="font-family: monospace; font-size: 18px; font-weight: bold; letter-spacing: 3px;"
                               ng-class="{'has-error': tokenForm.token.$invalid && tokenForm.token.$touched}">
                        <div class="help-block" ng-if="tokenForm.token.$invalid && tokenForm.token.$touched">
                            <small class="text-danger">Please enter a valid 6-digit token</small>
                        </div>
                    </div>

                    <!-- Error Message -->
                    <div class="alert alert-danger" ng-if="tokenInput.error">
                        <i class="fa fa-exclamation-triangle"></i>
                        {{tokenInput.error}}
                    </div>

                    <!-- Success Message -->
                    <div class="alert alert-success" ng-if="tokenInput.success">
                        <i class="fa fa-check-circle"></i>
                        {{tokenInput.success}}
                    </div>

                    <!-- Loading State -->
                    <div class="text-center" ng-if="tokenInput.validating">
                        <i class="fa fa-spinner fa-spin"></i>
                        Validating token...
                    </div>
                </form>

                <!-- Instructions -->
                <div class="panel panel-default" style="margin-top: 20px;">
                    <div class="panel-heading">
                        <h6 class="panel-title">
                            <i class="fa fa-info-circle"></i> Instructions
                        </h6>
                    </div>
                    <div class="panel-body">
                        <ul class="list-unstyled" style="margin: 0;">
                            <li><i class="fa fa-check text-success"></i> Get your 6-digit token from your supervisor</li>
                            <li><i class="fa fa-check text-success"></i> Enter the token exactly as provided</li>
                            <li><i class="fa fa-check text-success"></i> Each student has a unique token</li>
                            <li><i class="fa fa-check text-success"></i> Tokens are valid for the exam duration only</li>
                        </ul>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-default" data-dismiss="modal">Cancel</button>
                <button type="button" 
                        class="btn btn-primary" 
                        ng-click="submitToken()" 
                        ng-disabled="tokenForm.$invalid || tokenInput.validating || !tokenInput.token">
                    <i class="fa fa-unlock" ng-if="!tokenInput.validating"></i>
                    <i class="fa fa-spinner fa-spin" ng-if="tokenInput.validating"></i>
                    {{tokenInput.validating ? 'Validating...' : 'Access Quiz'}}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Token Input Styles -->
<style>
.token-input {
    font-family: 'Courier New', monospace;
    font-size: 24px;
    font-weight: bold;
    text-align: center;
    letter-spacing: 5px;
    padding: 15px;
}

.token-digit {
    display: inline-block;
    width: 40px;
    height: 50px;
    margin: 0 5px;
    text-align: center;
    font-size: 24px;
    font-weight: bold;
    border: 2px solid #ddd;
    border-radius: 5px;
    background: #f9f9f9;
}

.token-digit.filled {
    border-color: #5cb85c;
    background: #d4edda;
    color: #155724;
}

.modal-body .alert {
    margin-bottom: 15px;
}

.panel-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
}

.list-unstyled li {
    padding: 3px 0;
}

.list-unstyled li i {
    margin-right: 8px;
    width: 16px;
}
</style>

<!-- Enhanced Token Input with Individual Digits (Optional Enhancement) -->
<script type="text/ng-template" id="enhanced-token-input">
    <div class="token-digits-container" style="text-align: center; margin: 20px 0;">
        <input type="text" 
               class="token-digit" 
               ng-repeat="digit in [0,1,2,3,4,5] track by $index"
               ng-model="tokenInput.digits[$index]"
               maxlength="1"
               pattern="[0-9]"
               ng-class="{'filled': tokenInput.digits[$index]}"
               ng-keyup="moveToNext($event, $index)"
               ng-keydown="moveToPrev($event, $index)"
               id="digit{{$index}}">
    </div>
</script>
