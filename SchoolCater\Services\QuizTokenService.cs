using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Security.Cryptography;
using SchoolCater.Models;

namespace SchoolCater.Services
{
    /// <summary>
    /// In-memory token storage service for quiz access control
    /// </summary>
    public sealed class QuizTokenService
    {
        private static readonly Lazy<QuizTokenService> _instance = new Lazy<QuizTokenService>(() => new QuizTokenService());
        private readonly ConcurrentDictionary<string, QuizTokenInfo> _tokens = new ConcurrentDictionary<string, QuizTokenInfo>();
        private readonly object _lockObject = new object();

        public static QuizTokenService Instance => _instance.Value;

        private QuizTokenService() { }

        /// <summary>
        /// Generate tokens for all students eligible for a quiz
        /// </summary>
        /// <param name="quizId">Quiz ID</param>
        /// <param name="students">List of eligible students</param>
        /// <returns>List of generated token information</returns>
        public List<QuizTokenInfo> GenerateTokensForQuiz(int quizId, List<Student> students)
        {
            lock (_lockObject)
            {
                var tokenInfos = new List<QuizTokenInfo>();
                var generatedAt = DateTime.Now;
                var expiresAt = generatedAt.AddHours(4); // Tokens expire after 4 hours

                foreach (var student in students)
                {
                    var key = GenerateKey(student.Id, quizId);
                    var token = GenerateSecureToken();

                    var tokenInfo = new QuizTokenInfo
                    {
                        Token = token,
                        StudentId = student.Id,
                        StudentName = $"{student.FirstName} {student.LastName}",
                        RegistrationNumber = student.RegistrationNumber,
                        QuizId = quizId,
                        GeneratedAt = generatedAt,
                        ExpiresAt = expiresAt,
                        IsActive = true
                    };

                    _tokens.AddOrUpdate(key, tokenInfo, (k, v) => tokenInfo);
                    tokenInfos.Add(tokenInfo);
                }

                return tokenInfos;
            }
        }

        /// <summary>
        /// Validate a token for a specific student and quiz
        /// </summary>
        /// <param name="studentId">Student ID</param>
        /// <param name="quizId">Quiz ID</param>
        /// <param name="token">Token to validate</param>
        /// <returns>True if token is valid and active</returns>
        public bool ValidateToken(int studentId, int quizId, string token)
        {
            if (string.IsNullOrWhiteSpace(token))
                return false;

            var key = GenerateKey(studentId, quizId);
            
            if (_tokens.TryGetValue(key, out QuizTokenInfo tokenInfo))
            {
                // Check if token matches and is still active
                if (tokenInfo.Token.Equals(token, StringComparison.OrdinalIgnoreCase) && 
                    tokenInfo.IsActive && 
                    (tokenInfo.ExpiresAt == null || tokenInfo.ExpiresAt > DateTime.Now))
                {
                    return true;
                }
            }

            return false;
        }

        /// <summary>
        /// Get all tokens for a specific quiz
        /// </summary>
        /// <param name="quizId">Quiz ID</param>
        /// <returns>List of token information for the quiz</returns>
        public List<QuizTokenInfo> GetTokensForQuiz(int quizId)
        {
            return _tokens.Values
                .Where(t => t.QuizId == quizId)
                .OrderBy(t => t.StudentName)
                .ToList();
        }

        /// <summary>
        /// Regenerate token for a specific student and quiz
        /// </summary>
        /// <param name="studentId">Student ID</param>
        /// <param name="quizId">Quiz ID</param>
        /// <param name="studentName">Student name</param>
        /// <returns>New token information</returns>
        public QuizTokenInfo RegenerateToken(int studentId, int quizId, string studentName)
        {
            lock (_lockObject)
            {
                var key = GenerateKey(studentId, quizId);
                var newToken = GenerateSecureToken();
                var generatedAt = DateTime.Now;
                var expiresAt = generatedAt.AddHours(4);

                var tokenInfo = new QuizTokenInfo
                {
                    Token = newToken,
                    StudentId = studentId,
                    StudentName = studentName,
                    QuizId = quizId,
                    GeneratedAt = generatedAt,
                    ExpiresAt = expiresAt,
                    IsActive = true
                };

                _tokens.AddOrUpdate(key, tokenInfo, (k, v) => tokenInfo);
                return tokenInfo;
            }
        }

        /// <summary>
        /// Clear all tokens for a specific quiz
        /// </summary>
        /// <param name="quizId">Quiz ID</param>
        public void ClearTokensForQuiz(int quizId)
        {
            lock (_lockObject)
            {
                var keysToRemove = _tokens.Keys
                    .Where(key => _tokens[key].QuizId == quizId)
                    .ToList();

                foreach (var key in keysToRemove)
                {
                    _tokens.TryRemove(key, out _);
                }
            }
        }

        /// <summary>
        /// Mark a token as used/inactive
        /// </summary>
        /// <param name="studentId">Student ID</param>
        /// <param name="quizId">Quiz ID</param>
        public void MarkTokenAsUsed(int studentId, int quizId)
        {
            var key = GenerateKey(studentId, quizId);
            
            if (_tokens.TryGetValue(key, out QuizTokenInfo tokenInfo))
            {
                tokenInfo.IsActive = false;
            }
        }

        /// <summary>
        /// Clean up expired tokens
        /// </summary>
        public void CleanupExpiredTokens()
        {
            lock (_lockObject)
            {
                var expiredKeys = _tokens
                    .Where(kvp => kvp.Value.ExpiresAt.HasValue && kvp.Value.ExpiresAt < DateTime.Now)
                    .Select(kvp => kvp.Key)
                    .ToList();

                foreach (var key in expiredKeys)
                {
                    _tokens.TryRemove(key, out _);
                }
            }
        }

        /// <summary>
        /// Get total number of active tokens
        /// </summary>
        /// <returns>Count of active tokens</returns>
        public int GetActiveTokenCount()
        {
            return _tokens.Values.Count(t => t.IsActive && (t.ExpiresAt == null || t.ExpiresAt > DateTime.Now));
        }

        /// <summary>
        /// Generate a unique key for student-quiz combination
        /// </summary>
        /// <param name="studentId">Student ID</param>
        /// <param name="quizId">Quiz ID</param>
        /// <returns>Unique key string</returns>
        private string GenerateKey(int studentId, int quizId)
        {
            return $"{studentId}_{quizId}";
        }

        /// <summary>
        /// Generate a cryptographically secure 6-digit token
        /// </summary>
        /// <returns>6-digit numeric token</returns>
        private string GenerateSecureToken()
        {
            using (var rng = new RNGCryptoServiceProvider())
            {
                var bytes = new byte[4];
                rng.GetBytes(bytes);
                var randomNumber = Math.Abs(BitConverter.ToInt32(bytes, 0));
                return (randomNumber % 900000 + 100000).ToString(); // Ensures 6 digits
            }
        }
    }

    /// <summary>
    /// Token information model
    /// </summary>
    public class QuizTokenInfo
    {
        public string Token { get; set; }
        public int StudentId { get; set; }
        public string StudentName { get; set; }
        public string RegistrationNumber { get; set; }
        public int QuizId { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsActive { get; set; }
    }
}
