﻿// SuperAdminListController
adminSuperAdminApp.controller('SuperAdminListController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminSuperAdminWebAPIService', '$uibModal',
    '$state', '$stateParams', '$cookies',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminSuperAdminWebAPIService, $uibModal, $state, $stateParams, $cookies) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $scope.userType = "admin";
            $rootScope.title = "Super Administrators | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $scope.userType = "proprietor";
                $rootScope.title = "Super Administrators | Proprietor";
            }
            $rootScope.PageHeading = "Super Administrators";
            $rootScope.SubPageHeading = null;

            $scope.sortKey = "lastName";
            $scope.reverse = false;
            $scope.currentPage = 1;
            $scope.SearchResults = [];

            $scope.Filter = $stateParams;
            if ($scope.Filter.title !== undefined || $scope.Filter.lastName !== undefined || $scope.Filter.firstName !== undefined || $scope.Filter.middleName !== undefined
                 || $scope.Filter.gender !== undefined || $scope.Filter.phone !== undefined || $scope.Filter.email !== undefined) {
                $scope.Filtered = true;
                $scope.FilterView = {};
                for(var key in $scope.Filter)
                    $scope.FilterView[key] = $scope.Filter[key];
            }
            
            if ($stateParams.list == null)
                loadSuperAdmins($scope.Filtered); // get content
            else {
                $scope.OriginalList = $stateParams.list;
                if (!$scope.Filtered) $scope.SuperAdmins = $stateParams.list;
                else compileFilterList();
            }
        }

        function loadSuperAdmins(filtered) {
            $scope.contentLoading = true; // show loading icon

            var promiseGetSuperAdmins = AdminSuperAdminWebAPIService.getAllSuperAdmins();
            promiseGetSuperAdmins.then(function (pl) {
                $scope.SuperAdmins = pl.data;
                $scope.OriginalList = pl.data;
                if (filtered) compileFilterList();
            }, function (errorPl) {
                SharedMethods.createErrorToast("Problem loading information, please reload the page");
            })
            .finally(function () {
                $scope.contentLoading = false;
            });
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        }

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            var valid = ((row.title != null && angular.lowercase(row.title).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.lastName != null && angular.lowercase(row.lastName).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.firstName != null && angular.lowercase(row.firstName).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.middleName != null && angular.lowercase(row.middleName).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.gender != null && angular.lowercase(row.gender).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.phone != null && angular.lowercase(row.phone).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.email != null && angular.lowercase(row.email).indexOf(angular.lowercase($scope.searchText) || '') !== -1));
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember == null) $scope.SearchResults.push(row);
            }
            return valid;
        }

        $scope.reload = function () {
            $scope.searchText = "";
            if ($scope.Filtered) {
                $state.go('superadmin', {
                    title: null, lastName: null, firstName: null, middleName: null, gender: null, phone: null, email: null, list: null
                }, { reload: true });
            }
            else loadSuperAdmins(false);
        }

        $scope.toggleFilter = function () {
            $scope.showFilter = ($scope.showFilter === undefined || $scope.showFilter == false) ? true : false;
        }

        $scope.resetFilter = function () {
            $scope.Filter = {};
        }

        $scope.filter = function () {
            $state.go('superadmin', {
                title: $scope.Filter.title, lastName: $scope.Filter.lastName, firstName: $scope.Filter.firstName,
                middleName: $scope.Filter.middleName, gender: $scope.Filter.gender, phone: $scope.Filter.phone, email: $scope.Filter.email, list: $scope.OriginalList
            }, { reload: true });
        }

        function compileFilterList(){
            $scope.SuperAdmins = [];
            for (var index in $scope.OriginalList) {
                var add = true;
                add = ($stateParams.title === undefined ? true : $scope.OriginalList[index].title.toLowerCase() == $stateParams.title.toLowerCase())
                        && ($stateParams.lastName === undefined ? true : $scope.OriginalList[index].lastName.toLowerCase() == $stateParams.lastName.toLowerCase())
                        && ($stateParams.firstName === undefined ? true : $scope.OriginalList[index].firstName.toLowerCase() == $stateParams.firstName.toLowerCase())
                        && ($stateParams.middleName === undefined ? true : $scope.OriginalList[index].middleName.toLowerCase() == $stateParams.middleName.toLowerCase())
                        && ($stateParams.gender === undefined ? true : $scope.OriginalList[index].gender.toLowerCase() == $stateParams.gender.toLowerCase())
                        && ($stateParams.phone === undefined ? true : $scope.OriginalList[index].phone == $stateParams.phone)
                        && ($stateParams.email === undefined ? true : $scope.OriginalList[index].email.toLowerCase() == $stateParams.email.toLowerCase());
                if (add) $scope.SuperAdmins.push($scope.OriginalList[index]);
            }
            $scope.SearchResults = $scope.SuperAdmins;
        }

        $scope.deleteSuperAdmin = function (id, title, lastName, firstName) {
            var ModalModel = {
                Id: id,
                HeaderContent: "Delete " + title + ". " + firstName + " " + lastName,
                BodyMessage: "",
                ButtonText: "Delete",
                ButtonClass: "btn-danger"
            }

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'SuperAdminDeleteController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

        $scope.sendEmail = function (id) {
            var recipients = getAdmin(id);
            $cookies.put("recipients", JSON.stringify(recipients));
            window.location.href = "/" + $scope.userType + "/mail";
        }

        $scope.sendSMS = function (id) {
            var recipients = getAdmin(id);
            $cookies.put("recipients", JSON.stringify(recipients));
            window.location.href = "/" + $scope.userType + "/sms";
        }

        function getAdmin(id) {
            var recipients = [];
            $scope.OriginalList.some(function (obj) {
                if (obj.id == id) {
                    var temp = {
                        id: obj.id,
                        firstName: obj.firstName,
                        lastName: obj.lastName,
                        gender: obj.gender,
                        phone: obj.phone,
                        email: obj.email,
                        selected: true,
                        type: "SUPER ADMINISTRATOR"
                    }
                    recipients.push(temp);
                    return true;
                }
            });
            return recipients;
        }

    }]);

// SuperAdminDeleteController
adminSuperAdminApp.controller('SuperAdminDeleteController', ['$scope', '$uibModalInstance', 'AdminSuperAdminWebAPIService', '$state', 'ModalModel', 'SharedMethods',
    function ($scope, $uibModalInstance, AdminSuperAdminWebAPIService, $state, ModalModel, SharedMethods) {

        $scope.ModalModel = ModalModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);
            var promiseDelete = AdminSuperAdminWebAPIService.deleteSuperAdmin($scope.ModalModel.Id);

            promiseDelete.then(function (pl) {
                //dismiss modal
                $uibModalInstance.close();
                //redirect to List
                $state.go('superadmin', {}, { reload: true });
                //show success message
                SharedMethods.createSuccessToast('<strong>Super Administrator</strong> was deleted successfully!');
            },
            function (error) {
                //dismiss modal
                $uibModalInstance.close();
                //show error message
                SharedMethods.createErrorToast('Problem deleting <strong>Super Administrator</strong>. Try again!');
            });
        }
    }
]);

// SuperAdminInsertController
adminSuperAdminApp.controller('SuperAdminInsertController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminSuperAdminWebAPIService', '$state',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminSuperAdminWebAPIService, $state) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Add Super Administrator | Super Admin";
            if (window.location.href.indexOf("/proprietor/") !== -1) {
                $rootScope.title = "Add Super Administrator | Proprietor";
            }
            $rootScope.PageHeading = "Add Super Administrator";
            $rootScope.SubPageHeading = null;

            $scope.activeTab = 0;
            $scope.SuperAdmin = {};
            $scope.returnState = "superadmin";
            $scope.fieldErrors = {};

            $scope.Temp = {
                SuperAdminPicture: "/images/unknown.jpg"
            };
        }

        $rootScope.getParentUrl = function () {
            return $state.href('superadmin');
        };

        $scope.$watch('SuperAdmin.picture', function (newValue) {
            if (newValue !== undefined && newValue !== null)
                $scope.SuperAdmin.hasPicture = true;
        });

        $scope.removeProfilePicture = function () {
            $scope.SuperAdmin.profilePictureRemoved = true;
            $scope.SuperAdmin.hasPicture = false;
            $scope.SuperAdmin.picture = null;
            $scope.Temp.SuperAdminPicture = "/images/unknown.jpg";
        }

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-createAdmin'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var promisePostSuperAdmin = AdminSuperAdminWebAPIService.createSuperAdmin($scope.SuperAdmin);
            promisePostSuperAdmin.then(function (pl) {
                //redirect to list
                $state.go('superadmin');
                //show success message
                SharedMethods.createSuccessToast("<strong>Super Administrator</strong> was created successfully!");
            },
            function (error) {
                $scope.processing = false;
                submitBtn.stop();
                showAdminValidationErrors($scope, error, SharedMethods);
            });
        }
    }]);

// SuperAdminEditController
adminSuperAdminApp.controller('SuperAdminEditController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminSuperAdminWebAPIService', 'SuperAdmin', '$state',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminSuperAdminWebAPIService, SuperAdmin, $state) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Edit Super Administrator | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $rootScope.title = "Edit Super Administrator | Proprietor";
            }
            $rootScope.PageHeading = "Edit Super Administrator";
            $rootScope.SubPageHeading = null;

            $scope.activeTab = 0;
            $scope.SuperAdmin = SuperAdmin;
            $scope.returnState = "superadmin";
            $scope.fieldErrors = {};

            $scope.Temp = {
                SuperAdminPicture: "/api/visitorwebapi/getimage/" + $scope.SuperAdmin.id + "/PRINCIPAL/profile" + "?" + new Date().getTime()
            };
        }

        $rootScope.getParentUrl = function () {
            return $state.href('superadmin');
        };

        $scope.$watch('SuperAdmin.picture', function (newValue) {
            if (newValue !== undefined && newValue !== null)
                $scope.SuperAdmin.hasPicture = true;
        });

        $scope.removeProfilePicture = function () {
            $scope.SuperAdmin.profilePictureRemoved = true;
            $scope.SuperAdmin.hasPicture = false;
            $scope.SuperAdmin.picture = null;
            $scope.Temp.SuperAdminPicture = "/images/unknown.jpg";
        };

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-createAdmin'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var promisePutSuperAdmin = AdminSuperAdminWebAPIService.editSuperAdmin($scope.SuperAdmin.id, $scope.SuperAdmin);
            promisePutSuperAdmin.then(function (pl) {
                //redirect to list
                $state.go('superadmin');
                //show success message
                SharedMethods.createSuccessToast("<strong>Super Administrator</strong> profile was updated successfully!");
            },
            function (error) {
                $scope.processing = false;
                submitBtn.stop();
                showAdminValidationErrors($scope, error, SharedMethods);
            });
        }
    }]);

function showAdminValidationErrors($scope, error, SharedMethods, holder) {
    $scope.validationErrors = [];
    $scope.fieldErrors = {};
    var other = "";

    var switched = false;
    var tab1 = ["Picture", "Title", "LastName", "FirstName", "Gender"];
    var tab2 = ["Email", "Phone", "Address"];

    if (error.data && angular.isObject(error.data)) {
        modelErrors = error.data['modelState'];
        var count = 0;
        for (var key in modelErrors) {
            count++;
            value = modelErrors[key].toString();
            key = key.toString();
            if (key == "") {
                other = value;
                //$scope.validationErrors.push(value);
            }
            else {
                if (key.indexOf(".") != -1) //is in the form "object.property"
                    key = key.substr(key.indexOf(".") + 1);
                if (value.indexOf(".,") == -1)
                    $scope.fieldErrors[key] = value;
                else {
                    var arr = value.split(".,");
                    value = "";
                    for (var x in arr) value += arr[x] + "\n";
                    $scope.fieldErrors[key] = value;
                }

                // if tab has been switched to and key is in tab1, switch to tab 1
                if (!switched) {
                    if (tab1.indexOf(key) != -1) {
                        $scope.activeTab = 0;
                        switched = true;
                    }
                    else if (tab2.indexOf(key) != -1) {
                        $scope.activeTab = 1;
                        switched = true;
                    }
                    else if (tab3.indexOf(key) != -1) {
                        $scope.activeTab = 2;
                        switched = true;
                    }
                    else if (tab7.indexOf(key) != -1) {
                        $scope.activeTab = 6;
                        switched = true;
                    }
                }
            }
        }
        if (count > 0 && other === "")
            SharedMethods.createErrorToast("Please fill the form correctly!");
        else if (other !== "")
            SharedMethods.createErrorToast(other);
    } else {
        $scope.validationErrors.push('Problem occurred.');
    };
}


// Error404Controller
adminSuperAdminApp.controller('Error404Controller', ['$scope', '$rootScope',
    function ($scope, $rootScope) {
        $rootScope.title = "Page Not Found | School Cater";
        $rootScope.activePage = "";
        $rootScope.removeHeadSection = true;
    }]);