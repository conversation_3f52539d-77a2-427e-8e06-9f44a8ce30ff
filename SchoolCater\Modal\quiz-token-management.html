<div class="padding-two no-padding-bottom">
    <button type="button" class="close" style="padding: 10px" data-dismiss="modal" aria-hidden="true" ng-click="close()">&times;</button>
</div>
<div class="modal-header">
    <h4 class="modal-title font-weight-600">
        <i class="fa fa-key"></i> Quiz Access Tokens: {{tokenManagement.quiz.title}}
    </h4>
</div>
<div class="modal-body">
                <!-- Token Management Actions -->
                <div class="row" style="margin-bottom: 20px;">
                    <div class="col-md-12">
                        <div class="btn-toolbar" role="toolbar">
                            <div class="btn-group" role="group" style="margin-right: 10px; margin-top: 10px; margin-bottom: 5px;">
                                <button type="button" class="btn btn-primary" ng-click="generateAllTokens()" ng-disabled="tokenManagement.loading">
                                    <i class="fa fa-refresh" ng-class="{'fa-spin': tokenManagement.loading}"></i>
                                    Generate Tokens for All Students
                                </button>
                            </div>
                            <div class="btn-group" role="group" style="margin-right: 10px; margin-top: 10px; margin-bottom: 5px;">
                                <button type="button" class="btn btn-info" ng-click="copyAllTokens()" ng-disabled="!tokenManagement.tokens.length">
                                    <i class="fa fa-copy"></i>
                                    Copy All Tokens
                                </button>
                            </div>
                            <div class="btn-group" role="group" style="margin-right: 10px; margin-top: 10px; margin-bottom: 5px;">
                                <button type="button" class="btn btn-success" ng-click="printTokens()" ng-disabled="!tokenManagement.tokens.length">
                                    <i class="fa fa-print"></i>
                                    Print Tokens
                                </button>
                            </div>
                            <div class="btn-group" role="group" style="margin-top: 10px; margin-bottom: 5px;">
                                <button type="button" class="btn btn-warning" ng-click="clearAllTokens()" ng-disabled="!tokenManagement.tokens.length">
                                    <i class="fa fa-trash"></i>
                                    Clear All Tokens
                                </button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Token Statistics -->
                <div class="row" style="margin-bottom: 20px;" ng-if="tokenManagement.tokens.length">
                    <div class="col-md-12">
                        <div class="alert alert-info">
                            <strong>Token Statistics:</strong>
                            Total Students: {{tokenManagement.tokens.length}} |
                            Active Tokens: {{getActiveTokenCount()}} |
                            Expired Tokens: {{getExpiredTokenCount()}}
                            <span ng-if="tokenManagement.lastGenerated">
                                | Generated: {{tokenManagement.lastGenerated | date:'short'}}
                            </span>
                        </div>
                    </div>
                </div>

                <!-- Loading State -->
                <div ng-if="tokenManagement.loading" class="text-center" style="padding: 40px;">
                    <i class="fa fa-spinner fa-spin fa-3x"></i>
                    <p style="margin-top: 15px;">{{tokenManagement.loadingMessage}}</p>
                </div>

                <!-- No Tokens State -->
                <div ng-if="!tokenManagement.loading && !tokenManagement.tokens.length" class="text-center" style="padding: 40px;">
                    <i class="fa fa-key fa-3x text-muted"></i>
                    <h4 style="margin-top: 15px; color: #999;">No tokens generated yet</h4>
                    <p class="text-muted">Click "Generate Tokens for All Students" to create access tokens for this quiz.</p>
                </div>

                <!-- Tokens Table -->
                <div ng-if="!tokenManagement.loading && tokenManagement.tokens.length" class="table-responsive">
                    <table class="table table-striped table-hover">
                        <thead>
                            <tr>
                                <th>#</th>
                                <th>Student Name</th>
                                <th>Reg Number</th>
                                <th>Access Token</th>
                                <th>Generated At</th>
                                <th>Status</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr ng-repeat="token in tokenManagement.tokens | orderBy:'studentName'" ng-class="{'warning': !token.isActive}">
                                <td>{{$index + 1}}</td>
                                <td>
                                    <strong>{{token.studentName}}</strong>
                                </td>
                                <td>
                                    <span class="text-muted">{{token.registrationNumber}}</span>
                                </td>
                                <td>
                                    <div class="input-group input-group-sm" style="width: 150px;">
                                        <input type="text" class="form-control text-center" 
                                               value="{{token.token}}" readonly 
                                               style="font-family: monospace; font-weight: bold; font-size: 16px;">
                                        <span class="input-group-btn">
                                            <button class="btn btn-default" type="button" 
                                                    ng-click="copyToken(token.token)" 
                                                    title="Copy Token">
                                                <i class="fa fa-copy"></i>
                                            </button>
                                        </span>
                                    </div>
                                </td>
                                <td>
                                    <small class="text-muted">{{token.generatedAt | date:'short'}}</small>
                                </td>
                                <td>
                                    <span class="label" ng-class="{'label-success': token.isActive && !isTokenExpired(token), 'label-warning': !token.isActive, 'label-danger': isTokenExpired(token)}">
                                        {{getTokenStatus(token)}}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-xs btn-warning"
                                            ng-click="regenerateToken(token.studentId)"
                                            title="Regenerate Token">
                                        <i class="fa fa-refresh"></i> Regenerate
                                    </button>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Copy All Tokens Text Area (Hidden) -->
                <textarea id="allTokensText" style="position: absolute; left: -9999px; opacity: 0;"></textarea>
</div>
<div class="modal-footer">
    <button type="button" class="btn btn-default btn-round btn-small" ng-click="close()">Close</button>
    <button type="button" class="btn btn-primary btn-round btn-small" ng-click="refreshTokens()" ng-disabled="tokenManagement.loading">
        <i class="fa fa-refresh"></i> Refresh
    </button>
</div>

<!-- Print Template (Hidden) -->
<div id="printTokensTemplate" style="display: none;">
    <div style="padding: 20px; font-family: Arial, sans-serif;">
        <h2 style="text-align: center; margin-bottom: 30px;">
            Quiz Access Tokens
        </h2>
        <div style="margin-bottom: 20px;">
            <strong>Quiz:</strong> {{tokenManagement.quiz.title}}<br>
            <strong>Subject:</strong> {{tokenManagement.subject.name}}<br>
            <strong>Class:</strong> {{tokenManagement.classArm.name}}<br>
            <strong>Generated:</strong> {{tokenManagement.lastGenerated | date:'full'}}<br>
            <strong>Total Students:</strong> {{tokenManagement.tokens.length}}
        </div>
        <table style="width: 100%; border-collapse: collapse; margin-top: 20px;">
            <thead>
                <tr style="background-color: #f5f5f5;">
                    <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">#</th>
                    <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Student Name</th>
                    <th style="border: 1px solid #ddd; padding: 8px; text-align: left;">Reg Number</th>
                    <th style="border: 1px solid #ddd; padding: 8px; text-align: center;">Access Token</th>
                </tr>
            </thead>
            <tbody>
                <tr ng-repeat="token in tokenManagement.tokens | orderBy:'studentName'">
                    <td style="border: 1px solid #ddd; padding: 8px;">{{$index + 1}}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{token.studentName}}</td>
                    <td style="border: 1px solid #ddd; padding: 8px;">{{token.registrationNumber}}</td>
                    <td style="border: 1px solid #ddd; padding: 8px; text-align: center; font-family: monospace; font-weight: bold; font-size: 16px;">
                        {{token.token}}
                    </td>
                </tr>
            </tbody>
        </table>
        <div style="margin-top: 30px; font-size: 12px; color: #666;">
            <p><strong>Instructions:</strong></p>
            <ul>
                <li>Each student must enter their specific 6-digit token to access the quiz</li>
                <li>Tokens are valid for 4 hours from generation time</li>
                <li>Students must be in the designated exam area to use their tokens</li>
            </ul>
        </div>
    </div>
</div>
