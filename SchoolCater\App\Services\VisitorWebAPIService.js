﻿visitorApp.factory("VisitorWebAPIService", ['$http', 'SharedMethods', function ($http, SharedMethods) {

    var visitorBase = "/api/VisitorWebAPI/";
    var accBase = "/api/AccountWebAPI/";
    var visitorFactory = {};

    /*===== HOME =====*/
    var _getCurrentCustomers = function () {
        var request = $http({
            method: "get",
            url: visitorBase + "GetCurrentCustomers"
        });
        return request;
    };



    /*===== CONTACT US =====*/
    //Send an inquiry
    var _sendInquiry = function (Inquiry) {
        var request = $http({
            method: "post",
            url: visitorBase + "SendInquiry",
            data: Inquiry
        });
        return request;
    };



    /*===== AFFILIATE MARKETER =====*/
    // Register  
    var _registerAffiliateMarketer = function (Marketer) {
        var request = $http({
            method: "post",
            url: visitorBase + "ValidateMarketer",
            data: Marketer
        });
        return request;
    };



    /*===== RESULT CHECKER =====*/
    var _validateStudent = function (studentId) {
        var request = $http({
            method: "post",
            url: visitorBase + "ValidateResultChecker",
            data: JSON.stringify(studentId)
        });
        return request;
    };

    var _selectSchool = function (schoolId, studentRegNo) {
        // return $http.get(visitorBase + "SelectSchoolSessions/" + schoolId + "/" + studentRegNo);
        var request = $http({
            method: "post",
            url: visitorBase + "SelectSchoolSessions/" + schoolId,
            data: JSON.stringify(studentRegNo)
        });
        return request;
    };

    var _selectSession = function (sessionId, studentRegNo) {
        // return $http.get(visitorBase + "GetSessionTerms/" + sessionId + "/" + studentRegNo);
        var request = $http({
            method: "post",
            url: visitorBase + "GetSessionTerms/" + sessionId,
            data: JSON.stringify(studentRegNo)
        });
        return request;
    };

    var _getResult = function (ResultChecker) {
        var request = $http({
            method: "post",
            url: visitorBase + "VerifyResultCheckerComponentsNoComplete",
            data: ResultChecker
        });
        return request;
    };



    /*===== DISPLAY RESULT =====*/
    var _getReportSheet = function (token) {
        return $http.get(visitorBase + "GetReportSheetInfo/" + token).then(function (response) {
            return response.data;
        },
            function (error) {
                var reportSheet = {};
                return reportSheet;
            });
    };

    var _getUnlockedTerms = function (sessionId, token) {
        var request = $http({
            method: "get",
            url: visitorBase + "GetUnlockedTerms/" + sessionId + "/" + token
        });
        return request;
    };

    var _checkResult = function (session, term, token) {
        var request = $http({
            method: "post",
            url: visitorBase + "ReloadResult",
            data: { session: session, term: term, token: token }
        });
        return request;
    };

    var _generateResultPDF = function (ResultPDF) {
        var request = $http({
            method: "post",
            url: visitorBase + "GenerateReportSheetPDF",
            data: ResultPDF
        });
        return request;
    };



    visitorFactory.getCurrentCustomers = _getCurrentCustomers;

    visitorFactory.sendInquiry = _sendInquiry;

    visitorFactory.registerAffiliateMarketer = _registerAffiliateMarketer;

    visitorFactory.validateStudent = _validateStudent;
    visitorFactory.selectSchool = _selectSchool;
    visitorFactory.selectSession = _selectSession;
    visitorFactory.getResult = _getResult;

    visitorFactory.getReportSheet = _getReportSheet;
    visitorFactory.getUnlockedTerms = _getUnlockedTerms;
    visitorFactory.checkResult = _checkResult;
    visitorFactory.generateResultPDF = _generateResultPDF;

    return visitorFactory;
}]);