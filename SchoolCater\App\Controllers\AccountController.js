﻿// SignInController
accountApp.controller('SignInController', ['$rootScope', '$scope', 'AccountWebAPIService', 'SharedMethods', '$stateParams', '$cookies', '$window',
    function ($rootScope, $scope, AccountWebAPIService, SharedMethods, $stateParams, $cookies, $window) {
        $rootScope.title = "Sign In | School Cater";

        $scope.User = {};
        $scope.processing = false;
        $scope.DisplayPassword = false;
        var signInBtn = Ladda.create(document.querySelector('#sign_in_btn'));

        if ($stateParams.ReturnUrl)
            $scope.returnUrl = $stateParams.ReturnUrl;

        setupAccountPage($scope);

        $scope.togglePasswordDisplay = function () {
            $scope.DisplayPassword = !$scope.DisplayPassword;
        };

        $scope.signin = function () {
            $scope.processing = true;
            signInBtn.start();

            var User = {
                username: $scope.User.username,
                password: $scope.User.password
            };

            var promiseSignIn = AccountWebAPIService.signin(User);

            promiseSignIn.then(function (pl) {
                // remove existing cookie
                var expiryDate = new Date();
                $cookies.put("justLoggedIn", false, { expires: expiryDate, domain: document.domain });

                expiryDate.setMinutes(new Date().getMinutes() + 10);
                if (pl.data.indexOf("/group/") !== -1) {
                    $cookies.put("justLoggedIn", true, { expires: expiryDate, domain: document.domain });
                }

                $cookies.put("pendingHome", true, { expires: expiryDate, domain: document.domain });
                
                //redirect to return url if any
                if ($scope.returnUrl)
                    $window.location.href = $scope.returnUrl;
                // if not go to home page
                else
                    $window.location.href = pl.data;
            },
                function (error) {
                    signInBtn.stop();
                    SharedMethods.showValidationErrors($scope, error);//handle error
                    $scope.processing = false;
                });
        };
}]);

// ForgotPasswordController
accountApp.controller('ForgotPasswordController', ['$rootScope', '$scope', 'AccountWebAPIService', 'SharedMethods',
    function ($rootScope, $scope, AccountWebAPIService, SharedMethods) {
        $rootScope.title = "Forgot Password | School Cater";

        $scope.User = {};
        $scope.processing = false;
        $scope.sent = false;
        var sendBtn = Ladda.create(document.querySelector('#send_btn'));

        setupAccountPage($scope);

        $scope.process = function () {
            $scope.processing = true;
            sendBtn.start();

            var User = {
                username: $scope.User.username
            };

            var promiseRecoverAccount = AccountWebAPIService.recoverAccount(User);
            promiseRecoverAccount.then(function (pl) {
                $scope.sent = true;
            },
                function (error) {
                    SharedMethods.showValidationErrors($scope, error); //handle error
                })
                .finally(function () {
                    sendBtn.stop();
                    $scope.processing = false;
                });
        };
}]);

// ResetPasswordController
accountApp.controller('ResetPasswordController', ['$rootScope', '$scope', 'AccountWebAPIService', 'SharedMethods', '$stateParams',
    function ($rootScope, $scope, AccountWebAPIService, SharedMethods, $stateParams) {
        $rootScope.title = "Reset Password | School Cater";

        $scope.account = $stateParams.account;
        $scope.code = $stateParams.code;
        $scope.User = {};
        $scope.processing = false;
        $scope.reset = false;
        var resetBtn = Ladda.create(document.querySelector('#reset_btn'));

        setupAccountPage($scope);

        $scope.process = function () {
            resetBtn.start();
            $scope.processing = true;

            var User = {
                email: $scope.User.email,
                password: $scope.User.password,
                confirmPassword: $scope.User.confirmPassword,
                account: $scope.account,
                code: $scope.code
            };

            var promiseResetPassword = AccountWebAPIService.resetPassword(User);

            promiseResetPassword.then(function (pl) {
                resetBtn.stop();
                $scope.reset = true;
            },
                function (error) {
                    resetBtn.stop();
                    $scope.processing = false;
                    SharedMethods.showValidationErrors($scope, error);//handle error
                });
        };
    }]);

// RedirectExternalLogin
accountApp.controller('RedirectExternalLogin', ['$rootScope', 'AccountWebAPIService', '$stateParams', '$window', '$cookies',
    function ($rootScope, AccountWebAPIService, $stateParams, $window, $cookies) {
        $rootScope.title = "School Cater";

        // verify token
        var token = $stateParams.token;
        var promiseVerifyToken= AccountWebAPIService.verifyToken(token);

        promiseVerifyToken.then(function (pl) {
            if (pl.data.valid === "true") {
                // remove existing cookie
                var expiryDate = new Date();
                $cookies.put("justLoggedIn", false, { expires: expiryDate, domain: document.domain });

                expiryDate.setMinutes(new Date().getMinutes() + 10);
                if (pl.data.destination.indexOf("/group/") !== -1) {
                    $cookies.put("justLoggedIn", true, { expires: expiryDate, domain: document.domain });
                }

                $cookies.put("pendingHome", true, { expires: expiryDate, domain: document.domain });

                $window.location = pl.data.destination;
            }
            else {
                window.location = pl.data.destination;
            }
        },
        function (error) {
            if (error.data.destination === "home")
                $window.location = "/";
        });
    }]);


//RegisterController
accountApp.controller('RegisterController', ['$scope', '$rootScope', 'SharedMethods', 'AccountWebAPIService', '$timeout', '$cookies', '$sce',
    function ($scope, $rootScope, SharedMethods, AccountWebAPIService, $timeout, $cookies, $sce) {
        $rootScope.title = "Register | School Cater";

        $scope.School = {
            autoGenerateRegNo: "false",
            isGroup: "false"
        };
        $scope.Proprietor = {};
        $scope.Principal = {};
        $scope.Referral = {};
        $scope.fieldErrors = {};
        $scope.Complete = {};

        $scope.activeProgress = "school";
        $scope.activeView = "school";

        $scope.GroupSchoolTooltip = $sce.trustAsHtml("A group of schools is a school community consisting of different schools under single management.<br />For example,"
            + "Cater Group of Schools can consist of two schools: <br />(1) Cater Nursery and Primary School.<br /> (2) Cater College.<br />"
            + "This is made up of two schools managed by the same group.");

        $scope.SMSHeaderTooltip = $sce.trustAsHtml("This is the name shown when sending SMS using School Cater<br />"
            + "- SMS Header should not be more than 11 characters <br />"
            + "- SMS Header should not contain any space <br />"
            + "- Only use letters from A to Z");

        $scope.AutoRegNoTooltip = $sce.trustAsHtml("Would like to set up a pattern that would be used to automaically generate Registration Numbers for your students?");

        $scope.ReferralCodeTooltip = $sce.trustAsHtml("If you were referred to School Cater by one of our Affiliate Marketers, kindly enter their referral code here");

        setup();

        function setup() {
            setupAccountPage($scope);
            resizeAction();

            jQuery(window).resize(function () {
                resizeAction();
            });
        }

        function resizeAction() {
            var height = window.innerHeight;
            var section = $('#account-section');
            if (height < 900) {
                if (!section.hasClass('register-laptop')) {
                    section.addClass('register-laptop');
                }
            } else {
                section.removeClass('register-laptop');
            }
        }

        // return to saved position if any
        returnToLastPosition();

        function returnToLastPosition() {

            if ($cookies.getObject("registration") !== undefined) {
                var Registration = $cookies.getObject("registration");
                //School
                if (Registration.school !== undefined) {
                    $scope.School = Registration.school;
                    $scope.School.logo = null;
                    jQuery('#phone').val($scope.School.phone);
                }

                // Proprietor
                if (Registration.school !== undefined && Registration.proprietor !== undefined) {
                    $scope.Proprietor = Registration.proprietor;
                    $scope.Proprietor.picture = null;
                    jQuery('#phone2').val($scope.Proprietor.phone);
                }

                // Principal
                if (Registration.school !== undefined && Registration.proprietor !== undefined && Registration.principal !== undefined) {
                    $scope.Principal = Registration.principal;
                    $scope.Principal.picture = null;
                    jQuery('#phone3').val($scope.Principal.phone);
                }
            }
        }



        // SCHOOL
        var valSchool = Ladda.create(document.querySelector('#crSchool'));

        $scope.$watch('School.isGroup', function (value, oldValue) {
            if (value !== oldValue) {
                document.getElementById("schoolForm").reset();
                $scope.School = {
                    autoGenerateRegNo: "false",
                    isGroup: value
                };
                jQuery('#schoolForm .input-validation-error').removeClass('input-validation-error');
                $scope.fieldErrors = {};
                jQuery('input[name=groupQuest][value=' + value + ']').prop("checked", true);
                // $scope.Logo.school = "/images/logo-holder.jpg";
            }
        });

        $scope.validateSchool = function () {
            valSchool.start();
            $scope.fieldErrors = {};
            $scope.validatingSchool = true;
            jQuery('#schoolForm .input-validation-error').removeClass('input-validation-error');

            // If it is a school
            if ($scope.School.isGroup === "false" || $scope.School.isGroup === false) {
                var promiseValidateSchool = AccountWebAPIService.validateSchool($scope.School);
                promiseValidateSchool.then(function (pl) {
                    completeSchool( valSchool);
                },
                    function (error) {
                        $scope.validatingSchool = false;
                        valSchool.stop();
                        SharedMethods.showValidationErrors($scope, error);
                        SharedMethods.scrollView('body');
                    });
            }
            else {
                var promiseValidateGroup = AccountWebAPIService.validateGroup($scope.School);
                promiseValidateGroup.then(function (pl) {
                    completeSchool(valSchool);
                },
                    function (error) {
                        $scope.validatingSchool = false;
                        valSchool.stop();
                        SharedMethods.showValidationErrors($scope, error);
                        SharedMethods.scrollView('body');
                    });
            }
        };

        function completeSchool(valSchool) {

            $scope.validatingSchool = false;
            valSchool.stop();

            $scope.Complete.school = true;
            $scope.Complete.proprietor = false;
            $scope.activeView = "proprietor";
            jQuery('#proprietor_progress_item').addClass('progress-animate');
            $timeout(function () {
                $scope.activeProgress = "proprietor";
            }, 600);

            $timeout(function () {
                SharedMethods.scrollView('body');
            }, 300);
        }

        $scope.backToSchool = function () {
            $scope.Complete.school = false;
            $scope.activeView = "school";
            $scope.activeProgress = "school";
            jQuery('#proprietor_progress_item').removeClass('progress-animate');
            $scope.fieldErrors = {};
            jQuery('#schoolForm .input-validation-error').removeClass('input-validation-error');
            SharedMethods.scrollView('body');
        };


        // PROPRIETOR
        var valProprietor = Ladda.create(document.querySelector('#crProp'));

        $scope.validateProprietor = function () {
            valProprietor.start();
            $scope.fieldErrors = {};
            $scope.validatingProprietor = true;
            jQuery('#propForm .input-validation-error').removeClass('input-validation-error');

            var promiseValidateProprietor = AccountWebAPIService.validateProprietor($scope.Proprietor);
            promiseValidateProprietor.then(function (pl) {
                /*var Registration = $cookies.getObject("Registration") !== undefined ? $cookies.getObject("Registration") : {};
                if (Registration.school === undefined) Registration.school = $scope.School;
                Registration.proprietor = $scope.Proprietor;
                var expiryDate = new Date();
                expiryDate.setMinutes(new Date().getMinutes() + 10);
                $cookies.putObject("registration", Registration, { expires: expiryDate, domain: document.domain });*/

                $scope.validatingProprietor = false;
                valProprietor.stop();

                $scope.Complete.proprietor = true;
                $scope.Complete.principal = false;
                $scope.activeView = "principal";

                jQuery('#administrator_progress_item').addClass('progress-animate');
                $timeout(function () {
                    $scope.activeProgress = "principal";
                }, 600);

                /*if ($scope.Proprietor.isProprietor === "true" || $scope.Proprietor.isProprietor === true) {
                    $scope.Principal.middleName = "";
                    $scope.Principal.gender = "";
                    $scope.Principal.address = "";
                    $scope.Principal.logo = null;
                    jQuery('#files3').val('');
                }*/

                $timeout(function () {
                    SharedMethods.scrollView('body');
                }, 300);
            },
                function (error) {
                    $scope.validatingProprietor = false;
                    valProprietor.stop();
                    SharedMethods.showValidationErrors($scope, error);
                    SharedMethods.scrollView('body');
                });

        };

        $scope.backToProprietor = function () {
            $scope.Complete.proprietor = false;
            $scope.activeView = "proprietor";
            $scope.activeProgress = "proprietor";
            jQuery('#administrator_progress_item').removeClass('progress-animate');
            $scope.fieldErrors = {};
            jQuery('#propForm .input-validation-error').removeClass('input-validation-error');
            SharedMethods.scrollView('body');
        };


        // PRINCIPAL
        var valPrincipal = Ladda.create(document.querySelector('#crPrin'));
        $scope.validatePrincipal = function () {
            valPrincipal.start();
            $scope.fieldErrors = {};
            $scope.validatingPrincipal = true;
            jQuery('#prinForm .input-validation-error').removeClass('input-validation-error');

            var promiseValidatePrincipal = AccountWebAPIService.validateProprietor($scope.Principal);
            promiseValidatePrincipal.then(function (pl) {
                /*var Registration = $cookies.getObject("Registration") !== undefined ? $cookies.getObject("Registration") : {};
                if (Registration.school === undefined) Registration.school = $scope.School;
                if (Registration.proprietor === undefined) Registration.proprietor = $scope.Proprietor;
                Registration.principal = $scope.Principal;
                var expiryDate = new Date();
                expiryDate.setMinutes(new Date().getMinutes() + 10);
                $cookies.putObject("registration", Registration, { expires: expiryDate, domain: document.domain });*/

                $scope.validatingPrincipal = false;
                valPrincipal.stop();

                // ensure email and phone number of proprietor is not being used
                //if ($scope.Principal.phone == $scope.Proprietor.phone)
                //$scope.fieldErrors.phone = "Phone number is already ";
                if ($scope.Principal.email === $scope.Proprietor.email) {
                    $scope.fieldErrors.Email = "This email address is already in use.";
                }
                else {
                    $scope.Complete.principal = true;
                    $scope.Complete.final = false;
                    $scope.activeView = "final";

                    jQuery('#final_progress_item').addClass('progress-animate');
                    $timeout(function () {
                        $scope.activeProgress = "final";
                    }, 600);

                    $timeout(function () {
                        SharedMethods.scrollView('body');
                    }, 300);
                }
            },
                function (error) {
                    $scope.validatingPrincipal = false;
                    valPrincipal.stop();
                    SharedMethods.showValidationErrors($scope, error);
                    if ($scope.Principal.email === $scope.Proprietor.email)
                        $scope.fieldErrors.Email = "This email address is already in use.";
                    SharedMethods.scrollView('body');
                });

        };

        $scope.backToPrincipal = function () {
            $scope.Complete.principal = false;
            $scope.activeView = "principal";
            $scope.activeProgress = "principal";
            jQuery('#final_progress_item').removeClass('progress-animate');
            $scope.fieldErrors = {};
            jQuery('#prinForm .input-validation-error').removeClass('input-validation-error');
            SharedMethods.scrollView('body');
        };

        
        // COMPLETE REGISTRATION
        var completeRegBtn = Ladda.create(document.querySelector('#completeReg'));

        $scope.completeRegistration = function () {
            $scope.fieldErrors = {};
            completeRegBtn.start();
            $scope.completingRegistration = true;

            var CompleteRegistration = {
                school: $scope.School,
                proprietor: $scope.Proprietor,
                principal: $scope.Principal,
                referral: $scope.Referral
            };
            var promiseCompleteReg = AccountWebAPIService.completeRegistration(CompleteRegistration);
            promiseCompleteReg.then(function (pl) {
                // show message
                $scope.Complete.registration = true;

                // stop spinning
                $scope.completingRegistration = false;
                completeRegBtn.stop();
            },
                function (error) {
                    $scope.completingRegistration = false;
                    completeRegBtn.stop();
                    SharedMethods.showValidationErrors($scope, error);
                    if (error.data && angular.isObject(error.data)) {
                        modelErrors = error.data['modelState'];
                        for (var key in modelErrors) {
                            if (key.indexOf("school.") !== -1) {
                                $scope.Complete.school = false;
                                $scope.Complete.proprietor = false;
                                $scope.Complete.principal = false;
                                $scope.activeView = "school";
                                $scope.activeProgress = "school";
                                jQuery('#final_progress_item').removeClass('progress-animate');
                                jQuery('#principal_progress_item').removeClass('progress-animate');
                                jQuery('#proprietor_progress_item').removeClass('progress-animate');
                                break;
                            }
                            if (key.indexOf("prop.") !== -1) {
                                $scope.Complete.proprietor = false;
                                $scope.Complete.principal = false;
                                $scope.activeView = "proprietor";
                                $scope.activeProgress = "proprietor";
                                jQuery('#final_progress_item').removeClass('progress-animate');
                                jQuery('#principal_progress_item').removeClass('progress-animate');
                                break;
                            }
                            if (key.indexOf("prin.") !== -1) {
                                $scope.Complete.principal = false;
                                $scope.activeView = "principal";
                                $scope.activeProgress = "principal";
                                jQuery('#final_progress_item').removeClass('progress-animate');
                                break;
                            }
                            if (key.indexOf("referral.") !== -1) {
                                $scope.activeView = "final";
                                $scope.activeProgress = "final";
                                break;
                            }
                        }
                    }
                    SharedMethods.scrollView('body');
                });

        };

    }]);


function setupAccountPage($scope) {
    $scope.$on('$viewContentLoaded', function (_event, _viewConfig) {

        var delay = 600;

        function animateItems() {
            setTimeout(function () {

                jQuery('.animate').waypoint(function () {
                    var animation = jQuery(this).attr("data-animate");
                    jQuery(this).addClass(animation);
                    jQuery(this).addClass('animated');
                }, { offset: '80%' });
            }, delay);
        }

        animateItems();

    });
}


// Error404Controller
accountApp.controller('Error404Controller', ['$scope', '$rootScope',
    function ($scope, $rootScope) {
        $rootScope.title = "Page Not Found | School Cater";
        $rootScope.activePage = "";

        setupAccountPage($scope);
    }]);