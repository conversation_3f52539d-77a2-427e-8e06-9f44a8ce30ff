﻿accountApp.factory("AccountWebAPIService", ['$http', 'SharedMethods', function ($http, SharedMethods) {

    var accountBase = "/api/AccountWebAPI/";
    var visitorBase = "/api/VisitorWebAPI/";
    var accountFactory = {};


    // Recover Account
    var _recoverAccount = function (User) {
        var request = $http({
            method: "post",
            url: accountBase + "RecoverAccount",
            data: User
        });
        return request;
    };

    // Reset Password
    var _resetPassword = function (User) {
        var request = $http({
            method: "post",
            url: accountBase + "ResetPassword",
            data: User
        });
        return request;
    };

    // Sign in
    var _signin = function (User) {
        var request = $http({
            method: "post",
            url: accountBase + "SignIn",
            data: User
        });
        return request;
    };

    // Verify token for external login
    var _verifyToken = function (token) {
        var request = $http({
            method: "post",
            url: accountBase + "VerifySignInToken",
            data: JSON.stringify(token)
        });
        return request;
    };



    /*===== REGISTER =====*/
    var _validateSchool = function (School) {
        var request = $http({
            method: "post",
            url: visitorBase + "ValidateSchool",
            data: SharedMethods.getModelAsFormData(School),
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
        return request;
    };

    var _validateGroup = function (Group) {
        var request = $http({
            method: "post",
            url: visitorBase + "ValidateGroup",
            data: SharedMethods.getModelAsFormData(Group),
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
        return request;
    };

    var _validateProprietor = function (Proprietor) {
        var request = $http({
            method: "post",
            url: visitorBase + "ValidateProprietor",
            data: SharedMethods.getModelAsFormData(Proprietor),
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
        return request;
    };

    var _completeRegistration = function (RegistrationDetails) {
        var request = $http({
            method: "post",
            url: visitorBase + "CompleteRegistration",
            data: _getModelAsFormData2(RegistrationDetails),
            headers: { 'Content-Type': undefined },
            transformRequest: angular.identity
        });
        return request;
    };

    var _getModelAsFormData2 = function (data) {
        var dataAsFormData = new FormData();
        angular.forEach(data, function (value, key1) {
            angular.forEach(value, function (value2, key2) {
                if (value2 === null || value2 === undefined || value2 === "undefined") value2 = "";
                dataAsFormData.append(key1 + "." + key2, value2);
            });
        });
        return dataAsFormData;
    };


    accountFactory.recoverAccount = _recoverAccount;
    accountFactory.resetPassword = _resetPassword;
    accountFactory.signin = _signin;
    accountFactory.verifyToken = _verifyToken;

    accountFactory.validateSchool = _validateSchool;
    accountFactory.validateGroup = _validateGroup;
    accountFactory.validateProprietor = _validateProprietor;
    accountFactory.completeRegistration = _completeRegistration;

    return accountFactory;
}]);