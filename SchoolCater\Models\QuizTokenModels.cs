using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace SchoolCater.Models
{
    /// <summary>
    /// View model for quiz token management
    /// </summary>
    public class QuizTokenViewModel
    {
        public string Token { get; set; }
        public int StudentId { get; set; }
        public string StudentName { get; set; }
        public string RegistrationNumber { get; set; }
        public int QuizId { get; set; }
        public DateTime GeneratedAt { get; set; }
        public DateTime? ExpiresAt { get; set; }
        public bool IsActive { get; set; }
        public string Status => IsActive && (ExpiresAt == null || ExpiresAt > DateTime.Now) ? "Active" : "Expired";
    }

    /// <summary>
    /// Request model for generating quiz tokens
    /// </summary>
    public class GenerateQuizTokensRequest
    {
        [Required]
        public int QuizId { get; set; }
        
        [Required]
        public int SubjectId { get; set; }
        
        [Required]
        public int ClassArmId { get; set; }
    }

    /// <summary>
    /// Response model for generated quiz tokens
    /// </summary>
    public class GenerateQuizTokensResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public List<QuizTokenViewModel> Tokens { get; set; } = new List<QuizTokenViewModel>();
        public int TotalTokens { get; set; }
        public DateTime GeneratedAt { get; set; }
    }

    /// <summary>
    /// Request model for token validation
    /// </summary>
    public class ValidateTokenRequest
    {
        [Required]
        [StringLength(6, MinimumLength = 6, ErrorMessage = "Token must be exactly 6 digits")]
        public string Token { get; set; }
        
        [Required]
        public int QuizId { get; set; }
        
        [Required]
        public int SubjectId { get; set; }
    }

    /// <summary>
    /// Response model for token validation
    /// </summary>
    public class ValidateTokenResponse
    {
        public bool IsValid { get; set; }
        public string Message { get; set; }
        public int? StudentId { get; set; }
        public string StudentName { get; set; }
    }

    /// <summary>
    /// Request model for regenerating a specific student's token
    /// </summary>
    public class RegenerateTokenRequest
    {
        [Required]
        public int StudentId { get; set; }
        
        [Required]
        public int QuizId { get; set; }
        
        [Required]
        public int SubjectId { get; set; }
        
        [Required]
        public int ClassArmId { get; set; }
    }

    /// <summary>
    /// Response model for token regeneration
    /// </summary>
    public class RegenerateTokenResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public QuizTokenViewModel TokenInfo { get; set; }
    }

    /// <summary>
    /// Request model for clearing quiz tokens
    /// </summary>
    public class ClearQuizTokensRequest
    {
        [Required]
        public int QuizId { get; set; }
        
        [Required]
        public int SubjectId { get; set; }
        
        [Required]
        public int ClassArmId { get; set; }
    }

    /// <summary>
    /// Response model for clearing quiz tokens
    /// </summary>
    public class ClearQuizTokensResponse
    {
        public bool Success { get; set; }
        public string Message { get; set; }
        public int ClearedTokens { get; set; }
    }

    /// <summary>
    /// Model for quiz token statistics
    /// </summary>
    public class QuizTokenStats
    {
        public int QuizId { get; set; }
        public string QuizTitle { get; set; }
        public int TotalStudents { get; set; }
        public int TokensGenerated { get; set; }
        public int ActiveTokens { get; set; }
        public int ExpiredTokens { get; set; }
        public int UsedTokens { get; set; }
        public DateTime? LastGenerated { get; set; }
    }
}
