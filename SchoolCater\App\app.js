﻿/*===================== SHARED SPA ====================*/
var sharedApp = angular.module('SharedModule', ['ui.router', 'ui.bootstrap', 'ngToast', 'ngAnimate', 'ngSanitize', 'ngCookies', 'ngRoute', 'rt.resize'])
    .factory("ShareData", function () {
        return { value: 0 };
        })
    .service('SharedMethods', ['$uibModalStack', 'ngToast', '$window', function ($uibModalStack, ngToast, $window) {
    
        /* Modal */
        this.openLoadingModal = function () {
            jQuery("#loadingModalHolder").removeClass('ng-hide');
            jQuery("#loadingModalHolder2").removeClass('ng-hide');
        };

        this.closeLoadingModal = function () {
            jQuery("#loadingModalHolder").addClass('ng-hide');
            jQuery("#loadingModalHolder2").addClass('ng-hide');
        };

        this.keepModalOpen = function ($scope) {
            $scope.processing = true; // show loading gif
            $uibModalStack.getTop().value.keyboard = false;
            $uibModalStack.getTop().value.backdrop = 'static';
        };

        this.releaseModal = function ($scope) {
            $scope.processing = false; // show loading gif
            $uibModalStack.getTop().value.keyboard = true;
            $uibModalStack.getTop().value.backdrop = true;
        };

        /*Display validation errors*/
        this.showValidationErrors = function ($scope, error, holder, fillForm) {
            $scope.validationErrors = [];
            $scope.fieldErrors = {};
            var other = "";
            if (error.data && angular.isObject(error.data)) {
                modelErrors = error.data['modelState'];
                var count = 0;
                for (var key in modelErrors) {
                    count++;
                    value = modelErrors[key].toString();
                    key = key.toString();
                    if (key === "") {
                        other = value;
                        //$scope.validationErrors.push(value);
                    }
                    else if (key.indexOf(".ModelValue") !== -1) {
                        if (holder !== null && holder !== undefined)
                            $scope[holder][key.substr(0, key.indexOf(".ModelValue"))] = value;
                        else $scope[capitalize1(key.substr(0, key.indexOf(".ModelValue")))] = value;
                    }
                    else {
                        if (key.indexOf(".") !== -1) //is in the form "object.property"
                            key = key.substr(key.indexOf(".") + 1);
                        if (value.indexOf(".,") === -1)
                            $scope.fieldErrors[key] = value;
                        else {
                            var arr = value.split(".,");
                            value = "";
                            for (var x in arr) value += arr[x] + "\n";
                            $scope.fieldErrors[key] = value;
                        }
                    }
                }
                if (count > 0 && other === "" && fillForm !== false)
                    this.createErrorToast("Please fill the form correctly!");
                else if (other !== "")
                    this.createErrorToast(other);
            } else {
                $scope.validationErrors.push('Problem occurred.');
            }

        };

        /*Notifications*/
        this.createSuccessToast = function (cont, dismiss) {
            ngToast.success({
                content: '<i class="fa fa-thumbs-up alert-success margin-right-04"></i>' + cont,
                dismissOnTimeout: dismiss !== null && dismiss !== undefined ? dismiss : true
            });
        };

        this.createErrorToast = function (cont) {
            ngToast.danger({
                content: '<i class="fa fa-exclamation-triangle alert-danger margin-right-04"></i>' + cont
            });
        };

        this.dismissToasts = function () {
            ngToast.dismiss();
        };

        /* Preloader*/
        var preloaderDelay = 800;
        var preloaderFadeOutTime = 1000;

        this.hidePreloader = function () {
            var loadingAnimation = jQuery('#loading-animation');
            var preloader = jQuery('.main');

            loadingAnimation.fadeOut();
            preloader.delay(preloaderDelay).fadeOut(preloaderFadeOutTime);
        };

        /* Scroll & Resize */
        this.scrollView = function (e) {
            jQuery('html, body').animate({
                scrollTop: jQuery(e).offset().top - 70
            }, 1000);
        };

        this.resizeAction = function () {
            var width = $window.innerWidth;
            var sf = $('ul.sf-menu');
            if (width < 992) {
                if (sf.hasClass('sf-js-enabled')) {
                    sf.superfish('destroy');

                    $('ul.sf-menu a.sf-with-ul').parent().click(function (e) {
                        if ($(e.target).is('li'))
                            e.target.firstElementChild.nextElementSibling.classList.toggle('display-block');
                        else if ($(e.target).is('a') && e.target.nextElementSibling)
                            e.target.nextElementSibling.classList.toggle('display-block');
                        else if ($(e.target).is('i') && e.target.parentElement && e.target.parentElement.nextElementSibling)
                            e.target.parentElement.nextElementSibling.classList.toggle('display-block');
                    });
                }
            } else {
                if (!sf.hasClass('sf-js-enabled')) {
                    $('ul.sf-menu ul').removeClass('display-block');
                    $('ul.sf-menu a.sf-with-ul').unbind('click');
                    sf.superfish({
                        speed: 'fast',
                        delay: 100,
                        autoArrows: true
                    });
                }
            }
        };


        /* Form */
        this.getModelAsFormData = function (data) {
            var dataAsFormData = new FormData();
            angular.forEach(data, function (value, key) {
                if (value === null || value === undefined || value === "undefined") value = "";
                dataAsFormData.append(key, value);
            });
            return dataAsFormData;
        };

    }])
    .factory('myHttpInterceptor', function ($q, $injector, $location) {
        return {
            // sending out request
            'request': function (config) {
                // do something on success
                return config;
            },

            // successful response
            'response': function (response) {
                return response;
            },

            // error on response
            'responseError': function (rejection) {
                if (rejection.status === '404') {
                    state = $injector.get('$state');
                    state.go('404');
                    return $location.path();
                }
                else if (rejection.status === '401') {
                    //show sign in modal
                    //$rootScope.$broadcast("showSignInModal");
                }
                else if (rejection.status === '403') {
                    //alert('403');
                }
                return $q.reject(rejection);
            }
        };
        })
    .directive('intlTel', function () {
        return {
            replace: true,
            restrict: 'E', //E = element, A = attribute, C = class, M = comment
            require: 'ngModel',
            scope: {
                //@ reads the attribute value, = provides two-way binding, & works with functions
                ngModel: '='
            },
            template: '<input type="tel" name="phone" class="form-control">',
            //controller: controllerFunction, //Embed a custom controller in the directive
            link: function (scope, element, attrs, ngModel) { // DOM Manipulation
                element.intlTelInput({
                    defaultCountry: "ng",
                    utilsScript: "/Scripts/utils.js"
                });
            }
        };
    })
    .directive("layoutDir", function () {
        return {
            restrict: 'EA',
            controller: 'LayoutController',
            link: function ($scope, element, attrs) { }
        };
    })
    .controller("LayoutController", ['$scope', 'resize', 'SharedMethods',
        function ($scope, resize, SharedMethods) {

            setup();

            // Resize Callback
            resize($scope).call(function () {
                SharedMethods.resizeAction();
            });

            function setup() {   
                // scroll top when "back to top" button is pressed
                $('.back-top').bind('click', function (e) {
                    e.preventDefault();
                    $('html, body').animate({
                        scrollTop: $("html").offset().top
                    }, 1000);
                });

                // setup navigation drop-down
                $('.nav.navbar-nav').superfish({
                    speed: 'fast',
                    delay: 100,
                    autoArrows: true
                });


                var bar = jQuery('nav');
                var backToTop = jQuery('.back-top');
                $(window).scroll(function () {
                    // make navigation bar white with bottom shadow on scroll
                    if (jQuery(this).scrollTop() > 30) {
                        bar.addClass('sticky');
                    } else {
                        bar.removeClass('sticky');
                    }

                    // show back to top button
                    if (jQuery(this).scrollTop() > 180) {
                        backToTop.addClass('display-block');
                    } else {
                        backToTop.removeClass('display-block');
                    }
                });

                /*

                $(".chat-window-inner-content").niceScroll({ cursorcolor: "#c3c3c3", cursorwidth: "7px" });

                var justLoggedIn = $cookies.get("justLoggedIn") !== undefined ? $cookies.get("justLoggedIn") : null;
                if (justLoggedIn === "true") {
                    var expiryDate = new Date();
                    $cookies.put("justLoggedIn", false, { expires: expiryDate, domain: document.domain });
                    showSchoolSwitchPopUp();
                }*/
            }

            jQuery('.navbar-toggle').click(function (e) {
                let nav_bar = jQuery(e.target).parent()[0].nextElementSibling;
                if (!jQuery(nav_bar).hasClass('in')) {
                    $('ul.sf-menu a.sf-with-ul').each(function (_index, e) {
                        if ($(e.nextElementSibling).hasClass('display-block'))
                            $(e.nextElementSibling).removeClass('display-block');
                    });
                }
            });
        }])
    .config(function ($urlRouterProvider, $locationProvider, $stateProvider, $httpProvider, ngToastProvider, $uibModalProvider, $cookiesProvider) {
        //push interceptor
        $httpProvider.interceptors.push('myHttpInterceptor');

        // force lowercase links
        $urlRouterProvider.rule(function ($injector, $location) {
            var path = $location.path(), normalized = path.toLowerCase();
            if (path !== normalized) {
                $location.replace().path(normalized);
            }
        });

        //modal pop-up
        $uibModalProvider.options = {
            animation: true,
            backdrop: true,
            keyboard: true
        };

        // cookie configuration
        $cookiesProvider.defaults.path = "/";

        //toast notifications
        ngToastProvider.configure({
            verticalPosition: 'top',
            horizontalPosition: 'center',
            dismissOnTimeout: true,
            timeout: 4000,
            maxNumber: 3,
            animation: 'fade',
            dismissButton: true
        });

        //local storage configuration
        //localStorageServiceProvider.setPrefix("SchoolCater");

        //Redirect to 404 page if route is not found
        $urlRouterProvider.otherwise(function ($injector, $location) {
            var state = $injector.get('$state');
            state.go('404');
            return $location.path();
        });

        $stateProvider
            //404 page
            .state('404', {
                // no url defined
                controller: 'Error404Controller',
                templateUrl: 'OpenViews/Visitor/Error404.html'
            });

        $locationProvider.html5Mode(true).hashPrefix('!');
    })
    .run(function ($rootScope, SharedMethods) {
        $rootScope.$on('$stateChangeStart', function (event, toState, toStateParams, fromState, fromStateParams) {
            $rootScope.title = "School Cater";

            //if (toState.resolve) {
                jQuery('.main, #loading-animation').css('display', 'block');
            //}

            $rootScope.holdPreloader = false;

            //Remove all showing notifications
            SharedMethods.dismissToasts();
        });
        $rootScope.$on('$stateChangeSuccess', function (event, toState, toStateParams, fromState, fromStateParams) {
        
            if ($rootScope.holdPreloader === undefined || $rootScope.holdPreloader === null || $rootScope.holdPreloader === false)
                SharedMethods.hidePreloader();

            var move = location.hash.substr(1);
            if (move === null || move === "") {
                //scroll to page top
                //jQuery(document).scrollTop(0);
                SharedMethods.scrollView('body');
            }
            SharedMethods.closeLoadingModal();


            if ($('.navbar-collapse').hasClass('in'))
                $('.navbar-toggle').trigger('click');

            $('ul.sf-menu a.sf-with-ul').each(function (_index, e) {
                if ($(e.nextElementSibling).hasClass('display-block'))
                    $(e.nextElementSibling).removeClass('display-block');
            });
        });
    });


/*===================== VISITOR SPA ====================*/
var visitorApp = angular.module('VisitorModule', ['SharedModule'])
    .config(function ($stateProvider) {
        $stateProvider

            //home page
            .state('home', {
                url: '/',
                templateUrl: 'OpenViews/Visitor/Home.html',
                controller: 'HomeController'
            })

            // contact
            .state('contact', {
                url: '/contact',
                templateUrl: 'OpenViews/Visitor/Contact.html',
                controller: 'ContactController'
                })

            // faq
            .state('faq', {
                url: '/faq',
                templateUrl: 'OpenViews/Visitor/FAQ.html',
                controller: 'FAQController'
             })

            // features
            .state('features', {
                url: '/features',
                templateUrl: 'OpenViews/Visitor/Features.html',
                controller: 'FeaturesController'
            })

            // pricing
            .state('pricing', {
                url: '/pricing',
                templateUrl: 'OpenViews/Visitor/Pricing.html',
                controller: 'PricingController'
            })

            // what is school cater
            .state('what-is-school-cater', {
                url: '/what-is-school-cater',
                templateUrl: 'OpenViews/Visitor/WhatIsSchoolCater.html',
                controller: 'WhatIsSchoolCaterController'
            })

            // result checker page
            .state('result-checker', {
                url: '/result-checker',
                templateUrl: 'OpenViews/Visitor/ResultChecker.html',
                controller: 'ResultCheckerController'
            })

            // display result page
            .state('display-result', {
                url: '/display-result',
                params: { token: null },
                templateUrl: 'OpenViews/Visitor/DisplayResult.html',
                controller: 'DisplayResultController',
                resolve: {
                    ReportSheetInfo: function ($stateParams, VisitorWebAPIService, $rootScope) {
                        if ($stateParams.token === null) {
                            $rootScope.holdPreloader = true;
                            return {};
                        }
                        else {
                            return VisitorWebAPIService.getReportSheet($stateParams.token);
                        }
                    }
                }
            })

            // marketing
            .state('affiliate-marketing', {
                url: '/affiliate-marketing',
                templateUrl: 'OpenViews/Visitor/Marketing.html',
                controller: 'AffiliateMarketingController'
            })

            // terms of service
            .state('affiliate-marketing-terms', {
                url: '/affiliate-marketing-terms-and-conditions',
                templateUrl: 'OpenViews/Visitor/AffiliateMarketingTerms.html',
                controller: 'AffiliateMarketingTermsController'
            })

            // terms of service
            .state('terms-of-serice', {
                url: '/terms-of-service',
                templateUrl: 'OpenViews/Visitor/TermsOfService.html',
                controller: 'TermsOfServiceController'
            });
    });



/*========== ACCOUNT SPA ==========*/
var accountApp = angular.module('AccountModule', ['SharedModule'])
    .config(function ($stateProvider) {
        $stateProvider

            // register
            .state('register', {
                url: '/account/register',
                templateUrl: 'OpenViews/Account/Register.html',
                controller: 'RegisterController'
            })

            // sigin page
            .state('signin', {
                url: '/account/signin?ReturnUrl',
                templateUrl: 'OpenViews/Account/SignIn.html',
                controller: 'SignInController'
            })

            // forgotpassword
            .state('forgotpassword', {
                url: '/account/forgotpassword',
                templateUrl: 'OpenViews/Account/ForgotPassword.html',
                controller: 'ForgotPasswordController'
            })

            // resetpassword
            .state('resetpassword', {
                url: '/account/resetpassword?account&code',
                templateUrl: 'OpenViews/Account/ResetPassword.html',
                controller: 'ResetPasswordController'
            })

            // redirect external login
            .state('redirectlogin', {
                url: '/account/redirectlogin?token',
                templateUrl: 'UserAccount/RedirectExternalLogin',
                controller: 'RedirectExternalLogin',
                onEnter: function ($rootScope) {
                    $rootScope.holdPreloader = true;
                }
            });
        });