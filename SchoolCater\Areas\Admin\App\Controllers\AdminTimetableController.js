﻿
const days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];

// TimetableController
adminTimetableApp.controller('TimetableController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', '$state',
    '$stateParams', 'TimetableInfo', '$sce', 'Classes',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, $state, $stateParams, TimetableInfo, $sce, Classes) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        $scope.populateClassArms = function () {
            if ($scope.Filter.classId === undefined || $scope.Filter.classId == null || $scope.Filter.classId == '') {
                $scope.ClassArms = null;
            }
            else {
                $scope.Classes.some(function (obj) {
                    if (obj.id === $scope.Filter.classId) {
                        $scope.ClassArms = obj.classArms;
                        return true;
                    }
                });
            }
        };

        function populateClassArms() {
            if ($scope.Filter.classId === undefined || $scope.Filter.classId == null || $scope.Filter.classId == '') {
                $scope.ClassArms = null;
            }
            else {
                $scope.Classes.some(function (obj) {
                    if (obj.id == $scope.Filter.classId) {
                        $scope.ClassArms = obj.classArms;
                        return true;
                    }
                });
            }
        }

        function setup() {
            $rootScope.title = "Timetable | Super Admin";
            if (window.location.href.indexOf("/proprietor/") !== -1) {
                $rootScope.title = "Timetable | Proprietor";
            }
            $rootScope.PageHeading = "Timetable";
            $rootScope.SubPageHeading = null;

            $rootScope.hasLeftTab = false;

            $scope.Filter = {};
            $scope.Classes = Classes;

            $scope.Days = days;

            $scope.TimetableInfo = null;

            if (TimetableInfo.timeTableStructures.length > 0) {
                $scope.TimetableInfo = TimetableInfo;
                $scope.Sections = $scope.TimetableInfo.timeTableSections;

                $scope.Structures = $scope.TimetableInfo.timeTableStructures;
                $scope.StructureArray = [];
                for (var index in $scope.Structures) {
                    var str = $scope.Structures[index];
                    $scope.StructureArray[str.timetableStructureId] = str;
                }

                $scope.Subjects = $scope.TimetableInfo.subjects;
                $scope.SubjectsArr = chunk($scope.Subjects, 3);

                $scope.note = "Select a class arm to edit the timetable";
                if ($stateParams.classId !== undefined) {
                    $scope.Filter.classId = parseInt($stateParams.classId);
                    populateClassArms();
                    $scope.Filtered = true;

                    var className;
                    $scope.Classes.some(function (obj) {
                        if (obj.id === $scope.Filter.classId) {
                            className = obj.name;
                            return true;
                        }
                    });
                    $scope.ClassId = $stateParams.classId;
                    $scope.FilterView = { class: className };

                    if ($stateParams.classArmId !== undefined && $stateParams.classArmId !== "") {
                        $scope.Filter.classArmId = parseInt($stateParams.classArmId);

                        $scope.note = "Select a day to edit the timetable";
                        var classArmName;
                        $scope.ClassArms.some(function (obj) {
                            if (obj.id === $scope.Filter.classArmId) {
                                classArmName = obj.name;
                                return true;
                            }
                        });
                        $scope.ClassArmId = $stateParams.classArmId;
                        $scope.FilterView.classArm = classArmName;
                    }
                }
            }
            else {
                $scope.NoStructure = true;
                SharedMethods.createInfoToast("Time table has not been setup. Click &nbsp;"
                    + '<button ui-sref="timetable_structure" class="ladda-button text-uppercase font-13 icon-only" data-color="me3" data-style="slide-up">'
                    + '<i class= "fa fa-sliders-h font-14" ></i ></button> &nbsp;'
                    + "below to configure settings. <br/>(Note: Ensure each day of the week is included in a time table structure)", false);
            }
        }

        $scope.toggleFilter = function () {
            $scope.showFilter = ($scope.showFilter === undefined || $scope.showFilter === false) ? true : false;
        };

        $scope.resetFilter = function () {
            $scope.Filter = {};
            $scope.ClassArms = null;
            $('#classChooser option:selected').removeAttr('selected');
        };

        $scope.filter = function () {
            if ($scope.Filter.classId !== undefined && $scope.Filter.classId !== null && $scope.Filter.classId !== "") {
                $state.go('timetable_filter', { classId: $scope.Filter.classId, classArmId: $scope.Filter.classArmId }, { reload: true });
            }
            else {
                $state.go('timetable', {}, { reload: true });
            }
        };

        $scope.getLessonCode = function (sectionIndex, armIndex, columnIndex) {
            var subjectCode = "";
            $scope.Sections[sectionIndex].classArmTimetableContent[armIndex].periods.some(function (obj) {
                if (obj.periodIndex === columnIndex) {
                    if (subjectCode !== "")
                        subjectCode += "<span class='conflict'>" + obj.subjectCode + "</span>";
                    else
                        subjectCode = obj.subjectCode;
                    //return true;
                }
                else if (subjectCode !== "") return true;
            });
            return $sce.trustAsHtml(subjectCode);
        };
    }]);

function chunk(arr, columns) {
    var newArr = [];
    var arrLength = arr ? arr.length : 0;
    var size = Math.ceil(arrLength / columns--);
    var previousChunkSize = size;
    var gone = 0;
    for (let i = 0; i < arrLength; i += previousChunkSize) {
        newArr.push(arr.slice(i, i + size));
        gone += size;
        previousChunkSize = size;
        size = Math.ceil((arrLength - gone) / columns--);
    }
    return newArr;
}

// EditTimetableController
adminTimetableApp.controller('EditTimetableController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTimetableWebAPIService', 'ClassArm', '$stateParams',
    'TimetableInfo', '$state', '$compile',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTimetableWebAPIService, ClassArm, $stateParams, TimetableInfo, $state, $compile) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            // Class Arm Information
            $scope.ClassArm = ClassArm;
            $scope.ClassId = $stateParams.classId;
            if ($stateParams.formerClassArmId !== null) $scope.Parent = "ClassArm";
            else if ($stateParams.formerClassId !== null) $scope.Parent = "Class";
            else $scope.Parent = "Timetable";

            // Page Headings
            $rootScope.title = "Edit Timetable | Super Admin";
            if (window.location.href.indexOf("/proprietor/") !== -1) {
                $rootScope.title = "Edit Timetable | Proprietor";
            }
            $rootScope.PageHeading = "Edit Timetable";
            $rootScope.SubPageHeading = null;

            $scope.hasLeftTab = false;

            // Timetable Information
            $scope.Days = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday"];
            $scope.TimetableInfo = TimetableInfo;
            $scope.Content = $scope.TimetableInfo.timetableContent;
            $scope.Subjects = $scope.TimetableInfo.subjects;
            $scope.PeriodCount = -1;


            // Get the selected day
            $scope.SelectedDay = null;
            $scope.Days.some(function (obj) {
                if (obj.toLowerCase() === $stateParams.day.toLowerCase()) {
                    $scope.SelectedDay = obj;
                    return true;
                }
            });


            // Get the timetable structure for the chosen day
            $scope.StructureId = null;
            var dayIndex = $scope.Days.indexOf($scope.SelectedDay);
            $scope.TimetableInfo.timeTableSections.some(function (obj) {
                if (obj.days.indexOf(dayIndex) !== -1) {
                    $scope.StructureId = obj.timeTableStructureId;
                    return true;
                }
            });
            $scope.TimetableStructure = null;
            $scope.TimetableInfo.timeTableStructures.some(function (obj) {
                if (Number.parseInt(obj.timetableStructureId) === Number.parseInt($scope.StructureId)) {
                    $scope.TimetableStructure = obj;
                    return true;
                }
            });


            // transfer subject information
            $scope.Timetable = {
                day: dayIndex,
                periods: [],
                extraPeriods: []
            };
            var periods = $scope.TimetableInfo.timetableContent[dayIndex].periods;
            for (var index in periods) {
                var p = periods[index];
                var arrIndex = p.periodIndex - 1;
                if ($scope.Timetable.periods[arrIndex] === undefined) {
                    $scope.Timetable.periods[arrIndex] = {
                        subjectId: p.subjectId,
                        extraCount: 0
                    };
                }
                else {
                    // extra
                    var extraArrIndex = $scope.Timetable.periods[arrIndex].extraCount;
                    if ($scope.Timetable.extraPeriods[extraArrIndex] === undefined) $scope.Timetable.extraPeriods[extraArrIndex] = [];
                    $scope.Timetable.extraPeriods[extraArrIndex][arrIndex] = p.subjectId;
                    $scope.Timetable.periods[arrIndex].extraCount = $scope.Timetable.periods[arrIndex].extraCount + 1;
                }
            }

            setupPeriodArrays();
        }

        $rootScope.getParentUrl = function () {
            if ($scope.Parent === "Timetable")
                return $state.href('timetable');
            else if ($scope.Parent === "Class")
                return $state.href('timetable_filter', { classId: $scope.ClassArm.classId });
            else if ($scope.Parent === "ClassArm")
                return $state.href('timetable_filter', { classId: $scope.ClassArm.classId, classArmId: $scope.ClassArm.id });
        };

        function setupPeriodArrays() {
            // Periods before first break
            $scope.PBFB = chunk($scope.TimetableStructure.periodsBeforeFirstBreak, 2);

            // Periods after first break
            $scope.PAFB = chunk($scope.TimetableStructure.periodsAfterFirstBreak, 2);

            if ($scope.TimetableStructure.secondBreakStartTime !== null) {
                // Periods before second break
                $scope.PASB = chunk($scope.TimetableStructure.periodsAfterSecondBreak, 2);

                if ($scope.TimetableStructure.extraBreakStartTime !== null) {
                    // Periods after extra break
                    $scope.PAEB = chunk($scope.TimetableStructure.periodsAfterExtraBreak, 2);
                }
            }
        }

        $scope.increaseCounter = function () {
            $scope.PeriodCount += 1;
            return $scope.PeriodCount;
        };

        $scope.addLesson = function (parentCount) {
            var extraArr = $('#period' + parentCount + ' .position-relative').length - 1;
            if ($scope.Timetable.extraPeriods[extraArr] === undefined) $scope.Timetable.extraPeriods[extraArr] = [];
            var el = angular.element('#period' + parentCount);
            var htmlToAppend = '<div class="position-relative" id="period' + parentCount + '-' + extraArr + '">'
                + '<select class="form-control select-with-artificial-dropdown" ng-model="Timetable.extraPeriods[' + extraArr + '][' + parentCount + ']" ng-options="sub.id as sub.name for sub in Subjects" '
                + 'ng-class="{\'input-validation-error\': fieldErrors.AllPeriod[' + parentCount + ']}">'
                + '<option value="">--- Please Select ---</option></select>'
                + '<button class="button-like-link2 position-absolute input-inside-button red" style="top:0; right:0;" type="button" ng-click="removeLesson(' + extraArr + ',' + parentCount + ')" '
                + 'data-uib-tooltip="Remove Lesson" data-tooltip-placement="top" data-tooltip-append-to-body="false" data-tooltip-trigger="mouseenter">'
                + '<i class="fa fa-times i-extra-small-box"></i></button>'
                + '<button class="button-like-link2 position-absolute input-inside-button2 dummy-pointer" style="top:3px; right:45px; border:none !important" '
                + 'type="button"><i class="fa fa-angle-down i-extra-small-box"></i></button></div>';
            var compiledElement = $compile(htmlToAppend)($scope);
            el.append(compiledElement);
            if ($scope.Timetable.periods[parentCount] === undefined) $scope.Timetable.periods[parentCount] = {};
            if ($scope.Timetable.periods[parentCount].extraCount !== undefined)
                $scope.Timetable.periods[parentCount].extraCount = $scope.Timetable.periods[parentCount].extraCount + 1;
            else $scope.Timetable.periods[parentCount].extraCount = 1;
        };

        $scope.addExistingLesson = function (parentCount) {
            var period = $scope.Timetable.periods[parentCount];
            if (period !== undefined && period.extraCount !== undefined && period.extraCount > 0) {
                // add lesson for each extra
                for (var i = 0; i < period.extraCount; i++) {
                    var extraArr = angular.element('#period' + parentCount + ' .position-relative').length - 1;
                    if (extraArr >= period.extraCount) {
                        extraArr = extraArr - period.extraCount - 1;
                    }
                    var el = angular.element('#period' + parentCount);
                    var htmlToAppend = '<div class="position-relative" id="period' + parentCount + '-' + extraArr + '">'
                        + '<select class="form-control select-with-artificial-dropdown" ng-model="Timetable.extraPeriods[' + extraArr + '][' + parentCount + ']" ng-options="sub.id as sub.name for sub in Subjects" '
                        + 'ng-class="{\'input-validation-error\': fieldErrors.AllPeriod[' + parentCount + ']}">'
                        + '<option value="">--- Please Select ---</option></select>'
                        + '<button class="button-like-link2 position-absolute input-inside-button red" style="top:0; right:0;" type="button" ng-click="removeLesson(' + extraArr + ',' + parentCount + ')" '
                        + 'data-uib-tooltip="Remove Lesson" data-tooltip-placement="top" data-tooltip-append-to-body="false" data-tooltip-trigger="mouseenter">'
                        + '<i class="fa fa-times i-extra-small-box"></i></button>'
                        + '<button class="button-like-link2 position-absolute input-inside-button2 dummy-pointer" style="top:3px; right:45px; border:none !important" '
                        + 'type="button"><i class="fa fa-angle-down i-extra-small-box"></i></button></div>';
                    var compiledElement = $compile(htmlToAppend)($scope);
                    el.append(compiledElement);
                }
            }
        };

        $scope.removeLesson = function (extraArrIndex, parentCount) {
            // rollback values
            for (var i = extraArrIndex; i < $scope.Timetable.periods[parentCount].extraCount; i++) {
                if ($scope.Timetable.extraPeriods[i + 1] !== undefined && $scope.Timetable.extraPeriods[i + 1][parentCount] !== undefined)
                    $scope.Timetable.extraPeriods[i][parentCount] = $scope.Timetable.extraPeriods[i + 1][parentCount];
                else
                    $scope.Timetable.extraPeriods[i][parentCount] = null;
            }
            //remove html
            //var parentElement = angular.element('#period' + parentCount);
            //parentElement.remove(htmlToRemove);
            var htmlToRemove = $('#period' + parentCount + ' .position-relative').filter(':last').remove();
            //parentElement.removeChild(htmlToRemove);
            // reduce extraCount
            $scope.Timetable.periods[parentCount].extraCount = $scope.Timetable.periods[parentCount].extraCount - 1;
        };

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-createTimetable'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            // check for errors
            var errors = false;
            for (var index in $scope.Timetable.periods) {
                var foundExtra = false;
                var foundRepitition = false;

                var period = $scope.Timetable.periods[index];

                // execute for a period with extra lessons
                if (period.extraCount !== undefined && period.extraCount > 0) {

                    // check if subject was not chosen but has extra subjects chosen
                    if (period.subjectId === undefined || period.subjectId === null) {
                        for (var i = 0; i < period.extraCount; i++) {
                            if ($scope.Timetable.extraPeriods[i][index] !== undefined && $scope.Timetable.extraPeriods[i][index] !== null) {
                                foundExtra = true;
                                errors = true;
                                break;
                            }
                        }
                    }

                    // check for subject repititions
                    else if (period.subjectId !== undefined || period.subjectId !== null) {
                        var periodSubjects = [];
                        periodSubjects.push(period.subjectId);
                        for (let i = 0; i < period.extraCount; i++) {
                            var extraSubject = $scope.Timetable.extraPeriods[i][index];
                            if (extraSubject !== undefined && extraSubject !== null) {
                                periodSubjects.some(function (obj) {
                                    if (obj === extraSubject) {
                                        foundRepitition = true;
                                        errors = true;
                                        return true;
                                    }
                                });
                                periodSubjects.push(extraSubject);
                            }
                            //else
                            //  periodSubjects.push(extraSubject);

                            if (foundRepitition) break;
                        }
                    }
                }


                if (foundExtra) {
                    if ($scope.fieldErrors.Period === undefined) $scope.fieldErrors.Period = [];
                    $scope.fieldErrors.Period[index] = "Please select a subject.";
                }
                else if (foundRepitition) {
                    if ($scope.fieldErrors.Period === undefined) $scope.fieldErrors.Period = [];
                    $scope.fieldErrors.Period[index] = "Please remove duplicate subjects.";
                    if ($scope.fieldErrors.AllPeriod === undefined) $scope.fieldErrors.AllPeriod = [];
                    $scope.fieldErrors.AllPeriod[index] = "Please remove duplicate subjects.";
                }
            }

            if (!errors) {
                var promisePutClassArmTimetable = AdminTimetableWebAPIService.updateClassArmTimetable($scope.ClassArm.id, $scope.Timetable);
                promisePutClassArmTimetable.then(function (pl) {

                    //redirect to list
                    if ($scope.Parent === "Timetable")
                        return $state.go('timetable');
                    else if ($scope.Parent === "Class")
                        return $state.go('timetable_filter', { classId: $scope.ClassArm.classId });
                    else if ($scope.Parent === "ClassArm")
                        return $state.go('timetable_filter', { classId: $scope.ClassArm.classId, classArmId: $scope.ClassArm.id });

                    //show success message
                    SharedMethods.createSuccessToast("<strong>Timetable</strong> was updated successfully!");
                },
                    function (error) {
                        $scope.processing = false;
                        submitBtn.stop();
                        //showStudentValidationErrors($scope, error, SharedMethods);
                        //show error message
                        SharedMethods.createErrorToast("Problem updating <strong>Timetable</strong> information!");
                    });
            }
            else {
                SharedMethods.createErrorToast("Please fill the form correctly!");
                $scope.processing = false;
                submitBtn.stop();
            }
        };

    }]);




// TimetableStructureListController
adminTimetableApp.controller('TimetableStructureListController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTimetableWebAPIService', '$uibModal', '$state',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTimetableWebAPIService, $uibModal, $state) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Timetable Structures | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $rootScope.title = "Timetable Structures | Proprietor";
            }
            $rootScope.PageHeading = "Timetable Structures";
            $rootScope.SubPageHeading = null;

            $scope.sortKey = "name";
            $scope.reverse = false;

            $scope.currentPage = 1;
            $scope.SearchResults = [];
            $scope.All = {};
            $scope.Days = days;

            $rootScope.hasLeftTab = false;

            loadTimetableStructures();
        }

        $rootScope.getParentUrl = function () {
            return $state.href('timetable');
        };

        function loadTimetableStructures() {
            $scope.contentLoading = true; // show loading icon

            var promiseGetStructures = AdminTimetableWebAPIService.getAllTimetableStructures();
            promiseGetStructures.then(function (pl) {
                $scope.contentLoading = false;

                $scope.TimetableStructureInfo = pl.data;
                $scope.TimetableStructures = $scope.TimetableStructureInfo.timetableStructures;
                $scope.SearchResults = $scope.TimetableStructures;
            }, function (errorPl) {
                $scope.contentLoading = false;
                SharedMethods.createErrorToast("Problem loading information, please reload the page");
            })
            .finally(function () { });
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        }

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            //var cls = row.days;
            var valid = (row.name != null && angular.lowercase(row.name).indexOf(angular.lowercase($scope.searchText) || '') !== -1);
            if (!valid && row.days !== undefined && row.days != null && row.days.length > 0) {
                row.days.some(function (obj) {
                    valid = (angular.lowercase($scope.Days[obj.dayIndex]).indexOf(angular.lowercase($scope.searchText) || '') !== -1);
                    if (valid) return true;
                });
            }
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember == null) $scope.SearchResults.push(row);
            }
            return valid;
        }

        $scope.reload = function () {
            $scope.searchText = "";
            loadTimetableStructures();
        }

        $scope.removeTimetableStructureDay = function (structureIndex, dayIndex) {
            var structure = $scope.TimetableStructures[structureIndex];
            var day = structure.days[dayIndex];

            var ModalModel = {
                Id: day.dayIndex,
                HeaderContent: "Remove " + $scope.Days[day.dayIndex] + " from " + structure.name,
                BodyMessage: "All timetable entries for this day will be deleted.<br/>"
                    + "<label class='red no-margin-bottom margin-top-one'>Warning: This action cannot be reversed!!</label>",
                ButtonText: "Remove",
                ButtonClass: "btn-danger"
            }

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'DayRemoveController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

        $scope.deleteTimetableStructure = function (id, name) {
            var ModalModel = {
                Id: id,
                HeaderContent: "Delete \"" + name + "\"",
                BodyMessage: "The days assigned to this time table structure will be without a structure and the timetable entries for all class arms in these "
                    + "days will be deleted.<br/>"
                    + "<label class='red no-margin-bottom margin-top-one'>Warning: Proceed with caution!!</label>",
                ButtonText: "Delete",
                ButtonClass: "btn-danger"
            }

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'TimetableStructureDeleteController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

    }]);

// DayRemoveController
adminTimetableApp.controller('DayRemoveController', ['$scope', '$uibModalInstance', 'AdminTimetableWebAPIService', '$state', 'ModalModel', 'SharedMethods',
    function ($scope, $uibModalInstance, AdminTimetableWebAPIService, $state, ModalModel, SharedMethods) {

        $scope.ModalModel = ModalModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);
            var promiseDelete = AdminTimetableWebAPIService.removeDayFromTimetableStruture($scope.ModalModel.Id);

            promiseDelete.then(function (pl) {
                //dismiss modal
                $uibModalInstance.close();
                //redirect to List
                $state.go("timetable_structure", {}, { reload: true });
                //show success message
                SharedMethods.createSuccessToast('<strong>Day</strong> was removed successfully!');
            },
            function (error) {
                //dismiss modal
                $uibModalInstance.close();
                //show error message
                SharedMethods.createErrorToast('Problem removing <strong>Day</strong>. Try again!');
            });
        }
    }
]);

// TimetableStructureDeleteController
adminTimetableApp.controller('TimetableStructureDeleteController', ['$scope', '$uibModalInstance', 'AdminTimetableWebAPIService', '$state', 'ModalModel', 'SharedMethods',
    function ($scope, $uibModalInstance, AdminTimetableWebAPIService, $state, ModalModel, SharedMethods) {

        $scope.ModalModel = ModalModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);
            var promiseDelete = AdminTimetableWebAPIService.deleteTimetableStructure($scope.ModalModel.Id);

            promiseDelete.then(function (pl) {
                //dismiss modal
                $uibModalInstance.close();
                //redirect to List
                $state.go("timetable_structure", {}, { reload: true });
                //show success message
                SharedMethods.createSuccessToast('<strong>Timetable Structure</strong> was deleted successfully!');
            },
            function (error) {
                //dismiss modal
                $uibModalInstance.close();
                //show error message
                SharedMethods.createErrorToast('Problem deleting <strong>Timetable Structure</strong>. Try again!');
            });
        }
    }
]);


// InsertTimetableStructureController
adminTimetableApp.controller('InsertTimetableStructureController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'AdminTimetableWebAPIService',
    '$state', 'FreeDays', '$uibModal',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, AdminTimetableWebAPIService, $state, FreeDays, $uibModal) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Add Timetable Structure | Super Admin";
            if (window.location.href.indexOf("/proprietor/") !== -1) {
                $rootScope.title = "Add Timetable Structure | Proprietor";
            }
            $rootScope.PageHeading = "Add Timetable Structure";
            $rootScope.SubPageHeading = null;

            $scope.Days = days;
            $scope.TimetableStructure = {
                secondBreak: "false",
                extraClasses: "false"
            };

            $scope.FreeDays = FreeDays;

            $rootScope.hasLeftTab = false;

            $scope.HourRange = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
            $scope.MinuteRange = [];
            for (var i = 0; i <= 59; i++) {
                $scope.MinuteRange.push(i < 10 ? "0" + i : i);
            }
            $scope.DurationRange = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
            $scope.PeriodRange = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        }

        $rootScope.getParentUrl = function () {
            return $state.href('timetable_structure');
        };

        $scope.removeDay = function (index, name) {
            var ModalModel = {
                Id: index,
                HeaderContent: "Remove " + name,
                BodyMessage: "This day will no longer be associated with this timetable structure.",
                ButtonText: "Remove",
                ButtonClass: "btn-danger"
            }

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'DeleteController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    },
                    Array: function () {
                        return $scope.TimetableStructure.days;
                    },
                    DependentArray: function () {
                        return null;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

        $scope.addNewDay = function () {
            // create pop-up to choose parents
            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/DaySelect.html',
                controller: 'DaySelectController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return {
                            HeaderContent: "Select Days",
                            List: $scope.FreeDays,
                        };
                    },
                    SelectedDays: function () {
                        return $scope.TimetableStructure.days;
                    }
                }
            });
            modalInstance.opened.then(function () {
                //$scope.loadingClasses = false;
                //submitBtn.stop();
            });
            modalInstance.result.then(function (result) {
                $scope.TimetableStructure.days = result;
            });
        }

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-editTimetableStr'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var error = false;
            if ($scope.TimetableStructure.assemblyTimeObj === undefined) {
                error = true;
                $scope.fieldErrors.AssemblyTime = "Select the time the assembly starts.";
            }
            if ($scope.TimetableStructure.assemblyDuration == null) {
                error = true;
                $scope.fieldErrors.AssemblyDuration = "Select the duration of the assembly";
            }
            if ($scope.TimetableStructure.periodsBeforeFirstBreak == null) {
                error = true;
                $scope.fieldErrors.PeriodsBeforeFirstBreak = "Select the number of lesson periods after the assembly.";
            }
            if ($scope.TimetableStructure.periodsBeforeFirstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.PeriodsBeforeFirstBreakDuration = "Select the duration of lesson periods after the assembly.";
            }

            if ($scope.TimetableStructure.firstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.FirstBreakDuration = "Select the duration of the First Break.";
            }
            if ($scope.TimetableStructure.periodsAfterFirstBreak == null) {
                error = true;
                $scope.fieldErrors.PeriodsAfterFirstBreak = "Select the number of lesson periods after the First Break.";
            }
            if ($scope.TimetableStructure.periodsAfterFirstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.PeriodsAfterFirstBreakDuration = "Select the duration of lesson periods after the First Break.";
            }

            if ($scope.TimetableStructure.secondBreak == 'true') {
                if ($scope.TimetableStructure.secondBreakDuration == null) {
                    error = true;
                    $scope.fieldErrors.SecondBreakDuration = "Select the duration of the Second Break.";
                }
                if ($scope.TimetableStructure.periodsAfterSecondBreak == null) {
                    error = true;
                    $scope.fieldErrors.PeriodsAfterSecondBreak = "Select the number of lesson periods after the Second Break.";
                }
                if ($scope.TimetableStructure.periodsAfterSecondBreakDuration == null) {
                    error = true;
                    $scope.fieldErrors.PeriodsAfterSecondBreakDuration = "Select the duration of lesson periods after the Second Break.";
                }

                if ($scope.TimetableStructure.extraClasses == 'true') {
                    if ($scope.TimetableStructure.extraBreakDuration == null) {
                        error = true;
                        $scope.fieldErrors.ExtraBreakDuration = "Select the duration of the Extra Break.";
                    }
                    if ($scope.TimetableStructure.periodsAfterExtraBreak == null) {
                        error = true;
                        $scope.fieldErrors.PeriodsAfterExtraBreak = "Select the number of lesson periods after the Extra Break.";
                    }
                    if ($scope.TimetableStructure.periodsAfterExtraBreakDuration == null) {
                        error = true;
                        $scope.fieldErrors.PeriodsAfterExtraBreakDuration = "Select the duration of lesson periods after the Extra Break.";
                    }
                }
            }

            if (error) {
                $scope.activeTab = 0;
                SharedMethods.createErrorToast("Please fill the form correctly!");
                submitBtn.stop();
                $scope.processing = false;
            }
            else {
                $scope.TimetableStructure.assemblyTimeHour = $scope.TimetableStructure.assemblyTimeObj.getHours();
                $scope.TimetableStructure.assemblyTimeMinute = $scope.TimetableStructure.assemblyTimeObj.getMinutes();

                if ($scope.TimetableStructure.secondBreak == 'true') $scope.TimetableStructure.secondBreak = true;
                else $scope.TimetableStructure.secondBreak = false;
                if ($scope.TimetableStructure.extraClasses == 'true') $scope.TimetableStructure.extraClasses = true;
                else $scope.TimetableStructure.extraClasses = false;

                var promiseInsertTimetableStructure = AdminTimetableWebAPIService.createTimetableStructure($scope.TimetableStructure);
                promiseInsertTimetableStructure.then(function (pl) {
                    $scope.processing = false;

                    $state.go('timetable_structure');

                    //show success message
                    SharedMethods.createSuccessToast("<strong>Timetable Structure</strong> was created successfully!");

                    $scope.editDetails = false;
                },
                function (error) {
                    $scope.activeTab = 0;
                    $scope.processing = false;
                    submitBtn.stop();
                    SharedMethods.showValidationErrors($scope, error);
                    //showStaffValidationErrors($scope, error, SharedMethods);

                    if ($scope.TimetableStructure.secondBreak == true) $scope.TimetableStructure.secondBreak = "true";
                    else $scope.TimetableStructure.secondBreak = "false";
                    if ($scope.TimetableStructure.extraClasses == true) $scope.TimetableStructure.extraClasses = "true";
                    else $scope.TimetableStructure.extraClasses = "false";
                });
            }
        }
    }
]);

// EditTimetableStructureController
adminTimetableApp.controller('EditTimetableStructureController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'TimetableStructure',
    'AdminTimetableWebAPIService','$state', 'FreeDays', '$uibModal',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, TimetableStructure, AdminTimetableWebAPIService, $state, FreeDays, $uibModal) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Edit Timetable Structure | Super Admin";
            if (window.location.href.indexOf("/proprietor/") != -1) {
                $rootScope.title = "Edit Timetable Structure | Proprietor";
            }
            $rootScope.PageHeading = "Edit Timetable Structure";
            $rootScope.SubPageHeading = null;

            $scope.Days = days;
            $scope.TimetableStructure = TimetableStructure;
            var d = new Date();
            d.setHours($scope.TimetableStructure.assemblyTimeHour);
            d.setMinutes($scope.TimetableStructure.assemblyTimeMinute);
            $scope.TimetableStructure.assemblyTimeObj = d;

            if ($scope.TimetableStructure.secondBreak) $scope.TimetableStructure.secondBreak = "true";
            else $scope.TimetableStructure.secondBreak = "false";
            if ($scope.TimetableStructure.extraClasses) $scope.TimetableStructure.extraClasses = "true";
            else $scope.TimetableStructure.extraClasses = "false";

            $scope.OriginalDays = [];
            for (var index in $scope.TimetableStructure.days) {
                $scope.OriginalDays.push({
                    dayIndex: $scope.TimetableStructure.days[index].dayIndex
                });
            }

            $scope.FreeDays = FreeDays;
            for (var index in $scope.TimetableStructure.days) {
                $scope.FreeDays.push({
                    dayIndex: $scope.TimetableStructure.days[index].dayIndex,
                });
            }
            $scope.FreeDays.sort(dynamicSort("dayIndex"));

            $rootScope.hasLeftTab = false;

            $scope.HourRange = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
            $scope.MinuteRange = [];
            for (var i = 0; i <= 59; i++) {
                $scope.MinuteRange.push(i < 10 ? "0" + i : i);
            }
            $scope.DurationRange = [5, 10, 15, 20, 25, 30, 35, 40, 45, 50, 55, 60];
            $scope.PeriodRange = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10];
        }

        function dynamicSort(property) {
            var sortOrder = 1;
            if (property[0] === "-") {
                sortOrder = -1;
                property = property.substr(1);
            }
            return function (a, b) {
                var result = (a[property] < b[property]) ? -1 : (a[property] > b[property]) ? 1 : 0;
                return result * sortOrder;
            };
        }

        $rootScope.getParentUrl = function () {
            return $state.href('timetable_structure');
        };

        $scope.removeDay = function (index, name) {
            var ModalModel = {
                Id: index,
                HeaderContent: "Remove " + name,
                BodyMessage: "This day will no longer be associated with this timetable structure.",
                ButtonText: "Remove",
                ButtonClass: "btn-danger"
            };

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'DeleteController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    },
                    Array: function () {
                        return $scope.TimetableStructure.days;
                    },
                    DependentArray: function () {
                        return null;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }

        $scope.addNewDay = function () {
            // create pop-up to choose parents
            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/DaySelect.html',
                controller: 'DaySelectController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return {
                            HeaderContent: "Select Days",
                            List: $scope.FreeDays,
                        };
                    },
                    SelectedDays: function () {
                        return $scope.TimetableStructure.days;
                    }
                }
            });
            modalInstance.opened.then(function () {
                //$scope.loadingClasses = false;
                //submitBtn.stop();
            });
            modalInstance.result.then(function (result) {
                $scope.TimetableStructure.days = result;
            });
        }

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-editTimetableStr'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var error = false;

            // check for missing values
            if ($scope.TimetableStructure.assemblyDuration == null) {
                error = true;
                $scope.fieldErrors.AssemblyDuration = "Select the duration of the assembly";
            }
            if ($scope.TimetableStructure.periodsBeforeFirstBreak == null) {
                error = true;
                $scope.fieldErrors.PeriodsBeforeFirstBreak = "Select the number of lesson periods after the assembly.";
            }
            if ($scope.TimetableStructure.periodsBeforeFirstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.PeriodsBeforeFirstBreakDuration = "Select the duration of lesson periods after the assembly.";
            }

            if ($scope.TimetableStructure.firstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.FirstBreakDuration = "Select the duration of the First Break.";
            }
            if ($scope.TimetableStructure.periodsAfterFirstBreak == null) {
                error = true;
                $scope.fieldErrors.PeriodsAfterFirstBreak = "Select the number of lesson periods after the First Break.";
            }
            if ($scope.TimetableStructure.periodsAfterFirstBreakDuration == null) {
                error = true;
                $scope.fieldErrors.PeriodsAfterFirstBreakDuration = "Select the duration of lesson periods after the First Break.";
            }

            if ($scope.TimetableStructure.secondBreak == 'true') {
                if ($scope.TimetableStructure.secondBreakDuration == null) {
                    error = true;
                    $scope.fieldErrors.SecondBreakDuration = "Select the duration of the Second Break.";
                }
                if ($scope.TimetableStructure.periodsAfterSecondBreak == null) {
                    error = true;
                    $scope.fieldErrors.PeriodsAfterSecondBreak = "Select the number of lesson periods after the Second Break.";
                }
                if ($scope.TimetableStructure.periodsAfterSecondBreakDuration == null) {
                    error = true;
                    $scope.fieldErrors.PeriodsAfterSecondBreakDuration = "Select the duration of lesson periods after the Second Break.";
                }

                if ($scope.TimetableStructure.extraClasses == 'true') {
                    if ($scope.TimetableStructure.extraBreakDuration == null) {
                        error = true;
                        $scope.fieldErrors.ExtraBreakDuration = "Select the duration of the Extra Break.";
                    }
                    if ($scope.TimetableStructure.periodsAfterExtraBreak == null) {
                        error = true;
                        $scope.fieldErrors.PeriodsAfterExtraBreak = "Select the number of lesson periods after the Extra Break.";
                    }
                    if ($scope.TimetableStructure.periodsAfterExtraBreakDuration == null) {
                        error = true;
                        $scope.fieldErrors.PeriodsAfterExtraBreakDuration = "Select the duration of lesson periods after the Extra Break.";
                    }
                }
            }

            if (error) {
                $scope.activeTab = 0;
                SharedMethods.createErrorToast("Please fill the form correctly!");
                submitBtn.stop();
                $scope.processing = false;
            }
            else {
                // check if day was removed
                var missingDay = false;
                for (var index in $scope.OriginalDays) {
                    var found = false;
                    TimetableStructure.days.some(function (obj) {
                        if (obj.dayIndex == $scope.OriginalDays[index].dayIndex) {
                            found = true;
                            return true;
                        }
                    });
                    if (!found) {
                        missingDay = true;
                        break;
                    }
                }
                if (missingDay) {
                    var ModalModel = {
                        TimetableStructure: $scope.TimetableStructure,
                        HeaderContent: "SAVE CHANGES",
                        BodyMessage: "Please note that some days have been removed from this timetable structure. These days will need to be assigned to a timetable"
                            + " structure.<br/>",
                        ButtonText: "Save",
                        ButtonClass: "btn-primary"
                    }

                    var modalInstance = $uibModal.open({
                        templateUrl: 'Modal/Confirm.html',
                        controller: 'SaveDayChangesController',
                        openedClass: 'myModalBody',
                        resolve: {
                            ModalModel: function () {
                                return ModalModel;
                            },
                            ParentScope: function () {
                                return $scope;
                            }
                        }
                    });
                    modalInstance.opened.then(function () {
                        $scope.processing = false;
                        submitBtn.stop();
                    });
                    modalInstance.result.then(function () {
                    }, function () {
                    });
                }
                else {
                    $scope.TimetableStructure.assemblyTimeHour = $scope.TimetableStructure.assemblyTimeObj.getHours();
                    $scope.TimetableStructure.assemblyTimeMinute = $scope.TimetableStructure.assemblyTimeObj.getMinutes();

                    if ($scope.TimetableStructure.secondBreak == 'true') $scope.TimetableStructure.secondBreak = true;
                    else $scope.TimetableStructure.secondBreak = false;
                    if ($scope.TimetableStructure.extraClasses == 'true') $scope.TimetableStructure.extraClasses = true;
                    else $scope.TimetableStructure.extraClasses = false;

                    var promiseEditTimetableStructure = AdminTimetableWebAPIService.updateTimetableStructure($scope.TimetableStructure.id, $scope.TimetableStructure);
                    promiseEditTimetableStructure.then(function (pl) {
                        $scope.processing = false;

                        $state.go('timetable_structure');

                        //show success message
                        SharedMethods.createSuccessToast("<strong>Timetable Structure</strong> information was updated successfully!");

                        $scope.editDetails = false;
                    },
                    function (error) {
                        $scope.activeTab = 0;
                        $scope.processing = false;
                        submitBtn.stop();
                        SharedMethods.showValidationErrors($scope, error);
                        //showStaffValidationErrors($scope, error, SharedMethods);

                        if ($scope.TimetableStructure.secondBreak == true) $scope.TimetableStructure.secondBreak = "true";
                        else $scope.TimetableStructure.secondBreak = "false";
                        if ($scope.TimetableStructure.extraClasses == true) $scope.TimetableStructure.extraClasses = "true";
                        else $scope.TimetableStructure.extraClasses = "false";
                    });
                }
            }
        }
    }
]);

// SaveDayChangesController
adminTimetableApp.controller('SaveDayChangesController', ['$scope', '$uibModalInstance', 'AdminTimetableWebAPIService', '$state', 'ModalModel', 'SharedMethods', 'ParentScope',
    function ($scope, $uibModalInstance, AdminTimetableWebAPIService, $state, ModalModel, SharedMethods, ParentScope) {

        $scope.ModalModel = ModalModel;
        $scope.TimetableStructure = $scope.ModalModel.TimetableStructure;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);

            $scope.TimetableStructure.assemblyTimeHour = $scope.TimetableStructure.assemblyTimeObj.getHours();
            $scope.TimetableStructure.assemblyTimeMinute = $scope.TimetableStructure.assemblyTimeObj.getMinutes();

            if ($scope.TimetableStructure.secondBreak == 'true') $scope.TimetableStructure.secondBreak = true;
            else $scope.TimetableStructure.secondBreak = false;
            if ($scope.TimetableStructure.extraClasses == 'true') $scope.TimetableStructure.extraClasses = true;
            else $scope.TimetableStructure.extraClasses = false;

            var promiseEditTimetableStructure = AdminTimetableWebAPIService.updateTimetableStructure($scope.TimetableStructure.id, $scope.TimetableStructure);
            promiseEditTimetableStructure.then(function (pl) {
                //dismiss modal
                $uibModalInstance.close();

                $state.go('timetable_structure');

                //show success message
                SharedMethods.createSuccessToast("<strong>Timetable Structure</strong> information was updated successfully!");

                $scope.editDetails = false;
            },
            function (error) {
                ParentScope.activeTab = 0;

                //dismiss modal
                $uibModalInstance.close();

                if ($scope.TimetableStructure.secondBreak == true) $scope.TimetableStructure.secondBreak = "true";
                else $scope.TimetableStructure.secondBreak = "false";
                if ($scope.TimetableStructure.extraClasses == true) $scope.TimetableStructure.extraClasses = "true";
                else $scope.TimetableStructure.extraClasses = "false";


                SharedMethods.showValidationErrors($scope, error);
                //showStaffValidationErrors($scope, error, SharedMethods);
            });
        }
    }
]);

// DeleteController
adminTimetableApp.controller('DeleteController', ['$scope', 'ModalModel', '$uibModalInstance', 'Array', 'DependentArray',
    function ($scope, ModalModel, $uibModalInstance, Array, DependentArray) {
        $scope.ModalModel = ModalModel;
        $scope.Array = Array;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            if (DependentArray !== undefined && DependentArray !== null)
                delete DependentArray[$scope.Array[$scope.ModalModel.Id].id];
            $scope.Array.splice($scope.ModalModel.Id, 1);
            $uibModalInstance.close();
        }
    }]);

// DaySelectController
adminTimetableApp.controller('DaySelectController', ['$scope', '$uibModalInstance', 'ModalModel', 'SelectedDays',
    function ($scope, $uibModalInstance, ModalModel, SelectedDays) {
        $scope.ModalModel = ModalModel;

        setup();

        function setup() {
            $scope.All = {};
            $scope.Days = days;

            for (index in SelectedDays) {
                $scope.ModalModel.List.some(function (obj) {
                    if (obj.dayIndex == SelectedDays[index].dayIndex) {
                        obj.selected = true;
                        return true;
                    }
                });
            }
        }

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            $scope.SelectedDays = [];
            $scope.ModalModel.List.some(function (obj) {
                if (obj.selected) {
                    $scope.SelectedDays.push(obj);
                }
            });
            $uibModalInstance.close($scope.SelectedDays);
        }
    }]);



// Error404Controller
adminTimetableApp.controller('Error404Controller', ['$scope', '$rootScope',
    function ($scope, $rootScope) {
        $rootScope.title = "Page Not Found | School Cater";
        $rootScope.activePage = "";
        $rootScope.removeHeadSection = true;
    }]);