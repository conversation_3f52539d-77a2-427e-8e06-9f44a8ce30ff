﻿// HomeController
visitorApp.controller('HomeController', ['$scope', '$rootScope', 'SharedMethods', '$stateParams', 'VisitorWebAPIService',
    function ($scope, $rootScope, SharedMethods, $stateParams, VisitorWebAPIService) {
        $rootScope.title = "School Cater";
        $rootScope.activePage = "home";

        setup();

        setupVisitorPage($scope, SharedMethods);

        function setup() {
            // retrieve Customers
            let promiseGetCustomers = VisitorWebAPIService.getCurrentCustomers();
            promiseGetCustomers.then(function (pl) {
                //$scope.contentLoading = false;
                $scope.Customers = pl.data;

                // show carousel
                showCustomerCarousel();

            }, function (errorPl) {
                SharedMethods.createErrorToast("Problem loading information, please reload the page");
            })
                .finally(function () {
                    //$scope.contentLoading = false;
                });

            // scroll to section of page if indicated in url
            /*var move = location.hash.substr(1);
            if (move !== null && move !== "") {
                SharedMethods.scrollView('#' + move);
            }*/
        }

        $scope.$on('$viewContentLoaded', function (_event, _viewConfig) {

            $(".feedback-carousel").owlCarousel({
                loop: false,
                margin: 30,
                nav: false,
                autoHeight: true,
                responsive: {
                    0: {
                        items: 1,
                        nav: false
                    },
                    600: {
                        items: 2
                    },
                    1000: {
                        items: 3
                    }
                }
            });
        });

        function showCustomerCarousel() {
            var delay = 600;
            setTimeout(function () {
                $(".customer-carousel").owlCarousel({
                    loop: false,
                    margin: 30,
                    nav: false,
                    autoHeight: true,
                    responsive: {
                        0: {
                            items: 3,
                            nav: false
                        },
                        600: {
                            items: 4
                        },
                        1000: {
                            items: 6
                        }
                    }
                });
            }, delay);

        }
    }]);


// ContactController
visitorApp.controller('ContactController', ['$scope', '$rootScope', 'SharedMethods', 'VisitorWebAPIService',
    function ($scope, $rootScope, SharedMethods, VisitorWebAPIService) {
        $rootScope.title = "Contact | School Cater";
        $rootScope.activePage = "contact";
        $scope.Inquiry = {};
        $scope.sending = false;
        $scope.inquiring = false;
        

        // send inquiry to the school cater team
        $scope.sendInquiry = function () {
            var inquire = Ladda.create(document.querySelector('#sendInquire'));
            // start spinning button
            inquire.start();
            $scope.inquiring = true;

            // get recommendation info
            var Inquiry = {
                name: $scope.Inquiry.name,
                email: $scope.Inquiry.email,
                message: $scope.Inquiry.message
            };

            var promiseInquire = VisitorWebAPIService.sendInquiry(Inquiry);

            promiseInquire.then(function (pl) {
                inquire.stop();
                $scope.inquiring = false;

                if (pl.data === "success") {
                    SharedMethods.createSuccessToast("Your message was sent successfully, thank you!!", false);

                    $scope.Inquire = {};
                    document.getElementById("form5").reset();
                    $scope.validationErrors = [];
                    $scope.fieldErrors = {};
                }
                else {
                    SharedMethods.createErrorToast("Message was not sent, please try again");
                }
            },
                function (error) {
                    inquire.stop();
                    SharedMethods.showValidationErrors($scope, error);//handle error
                    $scope.inquiring = false;
                });
        };

        setupVisitorPage($scope, SharedMethods);
    }]);


// FAQController
visitorApp.controller('FAQController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "Frequently Asked Questions | School Cater";
        $rootScope.activePage = "faq";

        setupVisitorPage($scope, SharedMethods);

        $scope.$on('$viewContentLoaded', function (_event, _viewConfig) {

            setTimeout(function () {
                jQuery('.accordion .accordeon-title').click(function (e) {
                    let parentHeadingId = e.target.parentElement.id;
                    if (jQuery(e.target).hasClass('collapsed')) // clicked to open
                    {
                        // find other opened item
                        let acc = jQuery(".accordeon-item");
                        acc.each((e) => {
                            if (acc[e].firstElementChild.id !== parentHeadingId) {
                                if (!jQuery(acc[e].firstElementChild.firstElementChild).hasClass('collapsed')) {
                                    acc[e].firstElementChild.firstElementChild.click();
                                }
                            }
                        });
                    }

                });
            }, 600);
        });
    }]);


// FeaturesController
visitorApp.controller('FeaturesController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "Features | School Cater";
        $rootScope.activePage = "features";

        setupVisitorPage($scope, SharedMethods);
    }]);


// PricingController
visitorApp.controller('PricingController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "Pricing | School Cater";
        $rootScope.activePage = "pricing";

        setupVisitorPage($scope, SharedMethods);
    }]);


// WhatIsSchoolCaterController
visitorApp.controller('WhatIsSchoolCaterController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "What is School Cater | School Cater";
        $rootScope.activePage = "what-is-school-cater";

        setupVisitorPage($scope, SharedMethods);
    }]);


//ResultCheckerController
visitorApp.controller('ResultCheckerController', ['$scope', '$rootScope', 'SharedMethods', 'VisitorWebAPIService', '$timeout', '$state',
    function ($scope, $rootScope, SharedMethods, VisitorWebAPIService, $timeout, $state) {
        $rootScope.title = "Result Checker | School Cater";
        $rootScope.activePage = "result-checker";
        $scope.ResultChecker = {};
        $scope.Schools = [];
        $scope.Sessions = [];
        $scope.Terms = [];

        var oldVal = null;
        var validated = false;

        $scope.validateStudent = function () {
            var student_Id = $scope.ResultChecker.studentId !== undefined ? $scope.ResultChecker.studentId.trim() : "";
            if (student_Id !== null && student_Id !== "") {
                if (oldVal !== student_Id || oldVal === student_Id && !validated) {
                    oldVal = student_Id;

                    //send to server to see if valid
                    validateResultChecker(student_Id);
                }
            }
            else {
                //clear
                jQuery('.result_checker form select').html(" ");
            }
        };

        function validateResultChecker(studentId) {
            validated = false;
            $scope.validating = true;
            $scope.fieldErrors = {};
            $scope.ResultChecker.school = "";
            $scope.ResultChecker.session = "";
            $scope.ResultChecker.term = "";

            var promiseValidateStudent = VisitorWebAPIService.validateStudent(studentId);

            promiseValidateStudent.then(function (pl) {
                validated = true;
                $scope.validating = false;

                // clear form
                jQuery('.result_checker form select').html(" ");
                jQuery('.input-validation-error').removeClass('input-validation-error');

                // populate school section
                $scope.Schools = pl.data.schools;
                if ($scope.Schools.length > 1)
                    jQuery('#school').prepend('<option value="">----- Select School -----</option>');
                else {
                    $scope.ResultChecker.school = $scope.Schools[0].id;
                    $scope.selectSchool();
                }
                $timeout(function () {
                    jQuery("#school option[value='?']").remove();
                }, 0);
            },
                function (error) {
                    // clear form
                    jQuery('.result_checker form select').html(" ");
                    jQuery('.input-validation-error').removeClass('input-validation-error');

                    SharedMethods.showValidationErrors($scope, error);//handle error
                    if (error.data['resultChecker.StudentId']) {
                        $scope.fieldErrors['StudentId'] = error.data['resultChecker.StudentId'].errors[0].errorMessage;
                    }
                    $scope.validating = false;
                    SharedMethods.scrollView('body');
                });
        }

        $scope.selectSchool = function () {
            $scope.ResultChecker.session = "";
            $scope.ResultChecker.term = "";
            $scope.fieldErrors = {};
            jQuery('#session, #term').html(" ");
            jQuery('.input-validation-error').removeClass('input-validation-error');

            var schoolId = $scope.ResultChecker.school !== undefined ? $scope.ResultChecker.school : "";
            if (schoolId !== null && schoolId !== "") {
                $scope.generatingSessions = true;
                var promiseSelectSchool = VisitorWebAPIService.selectSchool($scope.ResultChecker.school, $scope.ResultChecker.studentId);

                promiseSelectSchool.then(function (pl) {
                    $scope.generatingSessions = false;

                    // populate session section
                    $scope.Sessions = pl.data;
                    jQuery('#session').prepend('<option value="">----- Select Session -----</option>');
                    $timeout(function () {
                        jQuery("#session option[value='?']").remove();
                    }, 0);
                },
                    function (error) {
                        $scope.generatingSessions = false;
                        SharedMethods.showValidationErrors($scope, error);
                        SharedMethods.scrollView('.result_checker');
                    });
            }
            else {
                //clear
                jQuery('#session, #term').html(" ");
            }
        };

        $scope.selectSession = function () {
            $scope.ResultChecker.term = "";
            $scope.fieldErrors = {};
            jQuery('#term').html(" ");
            jQuery('.input-validation-error').removeClass('input-validation-error');

            var sessionId = $scope.ResultChecker.session !== undefined ? $scope.ResultChecker.session : "";
            if (sessionId !== null && sessionId !== "") {
                $scope.generatingTerms = true;

                var promiseSelectSession = VisitorWebAPIService.selectSession($scope.ResultChecker.session, $scope.ResultChecker.studentId);

                promiseSelectSession.then(function (pl) {
                    $scope.generatingTerms = false;

                    // populate session section
                    $scope.Terms = pl.data;
                    jQuery('#term').prepend('<option value="">----- Select Term -----</option>');
                    $timeout(function () {
                        jQuery("#term option[value='?']").remove();
                    }, 0);
                },
                    function (error) {
                        $scope.generatingTerms = false;
                        SharedMethods.showValidationErrors($scope, error);//handle error
                        SharedMethods.scrollView('.result_checker');
                    });
            }
            else {
                //clear
                jQuery('#term').html(" ");
            }
        };

        $scope.getResult = function () {
            var submit_Btn = Ladda.create(document.querySelector('#check_result_btn'));
            submit_Btn.start();
            jQuery('.input-validation-error').removeClass('input-validation-error');
            $scope.fieldErrors = {};
            $scope.sending = true;

            var promiseGetResult = VisitorWebAPIService.getResult($scope.ResultChecker);

            promiseGetResult.then(function (pl) {
                var token = pl.data;
                $state.go('display-result', { token: token });
            },
                function (error) {
                    $scope.sending = false;
                    submit_Btn.stop();
                    SharedMethods.showValidationErrors($scope, error);//handle error
                    if (error.data['resultChecker.Serial']) {
                        $scope.fieldErrors['Serial'] = error.data['resultChecker.Serial'].errors[0].errorMessage;
                    }
                    SharedMethods.scrollView('.result_checker');
                });
        };
}]);


//DisplayResultController
visitorApp.controller('DisplayResultController', ['$scope', '$rootScope', 'SharedMethods', 'VisitorWebAPIService', 'ReportSheetInfo', '$timeout', '$stateParams', '$cookies', '$state',
    function ($scope, $rootScope, SharedMethods, VisitorWebAPIService, ReportSheetInfo, $timeout, $stateParams, $cookies, $state) {
        $rootScope.title = "Display Result | School Cater";
        $rootScope.activePage = "result-checker";
    
        if ($stateParams.token !== null && $stateParams.token !== undefined) {
            $scope.token = $stateParams.token;
            $cookies.put("tk", $scope.token, { expires: new Date().setHours(new Date().getHours + 1), domain: document.domain });
            $scope.ReportSheetInfo = angular.fromJson(ReportSheetInfo);
            initialiseResult();
        }
        else {
            if ($cookies.get("tk") === undefined)
                $state.go('result-checker');
            else {
                $scope.token = $cookies.get("tk");
                var promiseReportSheet = VisitorWebAPIService.getReportSheet($scope.token);
                promiseReportSheet.then(function (pl) {
                    $scope.ReportSheetInfo = angular.fromJson(pl);
                    initialiseResult();
                    SharedMethods.hidePreloader();
                },
                function (error) {
                    $state.go('result-checker');
                });
            }
        }

        function initialiseResult() {
            jQuery('.tooltipster').tooltipster({
                theme: 'tooltipster-me',
                touchDevices: false,
                position: 'bottom',
                hideOnClick: true
            });

            // Control Panel
            if ($scope.ReportSheetInfo.sessions === undefined || $scope.ReportSheetInfo.sessions === null)
                $state.go('result-checker');
            else {
                $scope.ResultSessions = $scope.ReportSheetInfo.sessions;
                $scope.ResultTerms = $scope.ReportSheetInfo.terms;
                $scope.ResultChecker = {
                    session: $scope.ReportSheetInfo.selectedSession,
                    term: $scope.ReportSheetInfo.selectedTerm
                };
                jQuery('#session').prepend('<option value="">----- Select School -----</option>');
                jQuery('#term').prepend('<option value="">----- Select Term -----</option>');
                $timeout(function () {
                    jQuery("#session option[value='?'], #term option[value='?']").remove();
                }, 0);

                // School Info
                var reportSheet = $scope.ReportSheetInfo.reportSheet;
                $scope.School = reportSheet.school;

                // Student Info
                $scope.Student = reportSheet.student;
                $scope.Term = reportSheet.term;

                // Cognitive Assessment
                $scope.TestTypes = reportSheet.testTypes;
                $scope.RecordedTestTypes = reportSheet.recordedTestTypes;
                $scope.Terms = reportSheet.terms;
                $scope.Cognitive = reportSheet.cognitive;
                $scope.TotalPercentage = 0;
                $scope.updateTotalPercentage = function (TestType) {
                    $scope.TotalPercentage += TestType.percentage;
                };

                // Cognitive Summary
                $scope.CogSum = reportSheet.cognitiveSummary;
                $scope.GradeKey = reportSheet.gradeKey;

                // Behavioral Assessment && Comment
                $scope.NonCognitiveAssessment = reportSheet.nonCognitiveAssessment;
                $scope.Comment = reportSheet.comment;
            }
        }

        // Select Session in Control Panel
        $scope.selectSession = function () {
            $scope.ResultChecker.term = "";
            $scope.fieldErrors = {};
            jQuery('#term').html(" ");
            jQuery('.input-validation-error').removeClass('input-validation-error');

            var sessionId = $scope.ResultChecker.session !== undefined ? $scope.ResultChecker.session : "";
            if (sessionId !== null && sessionId !== "") {
                $scope.generatingTerms = true;

                var promiseSelectSession = VisitorWebAPIService.getUnlockedTerms($scope.ResultChecker.session, $scope.token);

                promiseSelectSession.then(function (pl) {
                    if (pl.data.error)
                        $state.go("result-checker");
                    else {
                        $scope.generatingTerms = false;

                        // populate term section
                        $scope.ResultTerms = pl.data;
                        jQuery('#term').prepend('<option value="">----- Select Term -----</option>');
                        $timeout(function () {
                            jQuery("#term option[value='?']").remove();
                        }, 0);
                    }
                },
                    function (error) {
                        $scope.generatingTerms = false;
                        SharedMethods.showValidationErrors($scope, error);//handle error
                        SharedMethods.scrollView('#control-panel');
                    });
            }
            else {
                //clear
                jQuery('#term').html(" ");
            }
        };

        // Submit form to reload result
        $scope.checkResult = function () {
            var submit_Btn = Ladda.create(document.querySelector('#check_result_btn'));
            submit_Btn.start();
            $scope.fieldErrors = {};
            jQuery('.input-validation-error').removeClass('input-validation-error');
            $scope.checking = true;

            var promiseCheckResult = VisitorWebAPIService.checkResult($scope.ResultChecker.session, $scope.ResultChecker.term, $scope.token);
            promiseCheckResult.then(function (pl) {
                if (pl.data.error)
                    $state.go("result-checker");
                else {
                    $state.go('display-result', { token: pl.data }, { reload: true });
                }
            },
                function (error) {
                    submit_Btn.stop();
                    $scope.checking = false;
                    SharedMethods.showValidationErrors($scope, error);//handle error
                    SharedMethods.scrollView('#control-panel');
                });

        };

        // Generate pdf from result
        $scope.generatePdf = function () {
            var pdf_download = Ladda.create(document.querySelector('#pdf_download'));
            pdf_download.start();
            $scope.creating = true;

            var header = "";
            var links = jQuery('head').find('link[rel="stylesheet"]');
            for (var i = 0; i < links.length; i++) header += jQuery(links[i]).prop('outerHTML') + " ";

            var ResultPDF = {
                CssLinks: header,
                Html: jQuery('#register').html(),
                Token: $scope.token
            };

            var promiseGeneratePDF = VisitorWebAPIService.generateResultPDF(ResultPDF);
            promiseGeneratePDF.then(function (pl) {
                pdf_download.stop();
                if (pl.data.error)
                    $state.go("result-checker");
                else {
                    window.location = "/api/visitorwebapi/downloadfile/" + pl.data + "/" + $scope.token;
                }
            },
                function (error) {
                    pdf_download.stop();
                    $scope.checking = false;
                    SharedMethods.showValidationErrors($scope, error);//handle error
                    SharedMethods.scrollView('#control-panel');
                });

        };

        // Exit Display Result Page
        $scope.exit = function () {
            //remove token
            var expiryDate = new Date();
            $cookies.put("tk", false, { expires: expiryDate, domain: document.domain });
            //$cookies.remove('tk');
            $state.go('result-checker');
            // exit
        };
    }]);


// AffiliateMarketingController
visitorApp.controller('AffiliateMarketingController', ['$scope', '$rootScope', 'SharedMethods', '$uibModal',
    function ($scope, $rootScope, SharedMethods, $uibModal) {
        $rootScope.title = "Affiliate Marketing | School Cater";
        $rootScope.activePage = "affiliate-marketing";
        var joinBtn = Ladda.create(document.querySelector('#join_btn'));

        setupVisitorPage($scope, SharedMethods);

        $scope.openAffiliateRegistrationForm = function () {
            joinBtn.start();
            var modalInstance = $uibModal.open({
                templateUrl: 'OpenViews/Visitor/Modal/AffiliateRegistration.html',
                controller: 'AffiliateRegistrationController',
                openedClass: 'myModalBody'
            });
            modalInstance.opened.then(function () {
                joinBtn.stop();
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        };
    }]);

// AffiliateRegistrationController
visitorApp.controller('AffiliateRegistrationController', ['$scope', '$uibModalInstance', 'VisitorWebAPIService', 'SharedMethods',
    function ($scope, $uibModalInstance, VisitorWebAPIService, SharedMethods) {

        $scope.Marketer = {};
        $scope.DisplayPassword = false;
        $scope.registered = false;
        $scope.processing = false;

        $scope.togglePasswordDisplay = function () {
            $scope.DisplayPassword = !$scope.DisplayPassword;
        };

        $scope.close = function () {
            if (!$scope.processing)
                $uibModalInstance.dismiss('cancel');
        };

        $scope.register = function () {
            $scope.fieldErrors = {};
            SharedMethods.keepModalOpen($scope);
            var registerBtn = Ladda.create(document.querySelector('#register_btn'));
            registerBtn.start();
            $scope.processing = true;

            // check for valid password
            if ($scope.Marketer.password && $scope.Marketer.password.length < 6) {
                $scope.fieldErrors.Password = "Password must have atleast 6 characters";
                $scope.processing = false;
                registerBtn.stop();
            }
            else {
                var promiseRegisterMarketer = VisitorWebAPIService.registerAffiliateMarketer($scope.Marketer);
                promiseRegisterMarketer.then(function (pl) {
                    registerBtn.stop();
                    $scope.processing = false;
                    $scope.registered = true;
                    SharedMethods.releaseModal($scope);
                },
                    function (error) {
                        $scope.processing = false;
                        registerBtn.stop();
                        SharedMethods.showValidationErrors($scope, error);
                    });
            }

        };
    }
]);


// AffiliateMarketingTermsController
visitorApp.controller('AffiliateMarketingTermsController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "Affiliate Marketing Terms | School Cater";
        $rootScope.activePage = "affiliate-marketing-terms";

        setupVisitorPage($scope, SharedMethods);
    }]);


// TermsOfServiceController
visitorApp.controller('TermsOfServiceController', ['$scope', '$rootScope', 'SharedMethods',
    function ($scope, $rootScope, SharedMethods) {
        $rootScope.title = "Terms of Service | School Cater";
        $rootScope.activePage = "";

        setupVisitorPage($scope, SharedMethods);
    }]);


// Error404Controller
visitorApp.controller('Error404Controller', ['$scope', '$rootScope', 'SharedMethods', function ($scope, $rootScope, SharedMethods) {
    $rootScope.activePage = "";
    $rootScope.title = "Page Not Found | School Cater";

    setupVisitorPage($scope, SharedMethods);
}]);

function setupVisitorPage($scope, SharedMethods) {
    $scope.$on('$viewContentLoaded', function (_event, _viewConfig) {

        var delay = 600;

        function animateItems() {
            setTimeout(function () {

                jQuery('.animate').waypoint(function () {
                    var animation = jQuery(this).attr("data-animate");
                    jQuery(this).addClass(animation);
                    jQuery(this).addClass('animated');
                }, { offset: '80%' });
            }, delay);
        }

        animateItems();
        SharedMethods.resizeAction();
    });
}
