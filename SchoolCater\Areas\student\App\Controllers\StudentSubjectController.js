﻿const days = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
const monthNames = ["January", "February", "March", "April", "May", "June", "July", "August", "September", "October", "November", "December"];

/*========== SUBJECT BASIC INFO ==========*/
// SubjectListController
studentSubjectApp.controller('SubjectListController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'StudentSubjectWebAPIService', '$uibModal', '$state',
    '$stateParams',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, StudentSubjectWebAPIService, $uibModal, $state, $stateParams) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Subjects | Student";
            $rootScope.PageHeading = "Subjects";
            $rootScope.SubPageHeading = null;

            $scope.sortKey = "name";
            $scope.reverse = false;

            $rootScope.hasLeftTab = false;

            $scope.currentPage = 1;
            $scope.SearchResults = [];
            $scope.All = {};

            loadSubjects();
        }

        function loadSubjects() {
            $scope.contentLoading = true; // show loading icon

            var promiseGetSubjects = StudentSubjectWebAPIService.getAllSubjects();
            promiseGetSubjects.then(function (pl) {
                $scope.contentLoading = false;

                $scope.Subjects = pl.data;
                $scope.SearchResults = pl.data;
            }, function (errorPl) {
                $scope.contentLoading = false;
                SharedMethods.createErrorToast("Problem loading information, please reload the page");
            })
            .finally(function () { });
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        };

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            var valid = (row.name !== null && angular.lowercase(row.name).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.code !== null && angular.lowercase(row.code).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.description !== null && angular.lowercase(row.description).indexOf(angular.lowercase($scope.searchText) || '') !== -1);
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember === null) $scope.SearchResults.push(row);
            }
            return valid;
        };

        $scope.reload = function () {
            $scope.searchText = "";
            loadSubjects();
        };
    }]);

// SubjectInfoTemplateController
studentSubjectApp.controller('SubjectInfoTemplateController', ['$scope', '$rootScope', 'Subject', '$state',
    function ($scope, $rootScope, Subject, $state) {
        //if (!SharedMethods.redirect(RedirectInfo)) {
        setup();
        //}

        function setup() {
            $scope.Subject = Subject;
            $rootScope.hasLeftTab = true;
            $rootScope.PageHeading = $scope.Subject.name;
        }

        $rootScope.getParentUrl = function () {
            return $state.href('subject');
        };
    }]);

// BasicSubjectInfoController
studentSubjectApp.controller('BasicSubjectInfoController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Subject | Student";
            $rootScope.SubPageHeading = "Basic Information";
            $rootScope.activeView = "basic";

            $scope.Subject = Subject;
        }
    }
]);




/*========== SUBJECT CURRICULUM ==========*/
// SubjectCurriculumController
studentSubjectApp.controller('SubjectCurriculumController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject', 'MyClassArm', '$state',
    'Terms', '$stateParams', 'CurriculumInfo',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject, MyClassArm, $state, Terms, $stateParams, CurriculumInfo) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Curriculum | Student";
            $rootScope.SubPageHeading = "Curriculum";
            $rootScope.activeView = "curriculum";

            $scope.Filter = {};
            $scope.showFilter = true;
            $scope.editDetails = false;
            $scope.NotAdmin = true;
            $scope.isParent = true
            $scope.SelectedClass = { name: MyClassArm.className };

            if (CurriculumInfo != null) {
                $scope.Curriculum = CurriculumInfo.list;
                if (CurriculumInfo.list == undefined || CurriculumInfo.list == null) {
                    // create empty list
                    $scope.Curriculum = [];
                    for (var i = 0; i < CurriculumInfo.weeks; i++) {
                        $scope.Curriculum.push(
                            {
                                "week": (i + 1),
                                "topic": null,
                                "details": null
                            });
                    }
                }
            }
            $scope.Subject = Subject;

            $scope.Terms = Terms;
            $scope.SelectedTerm = null;
            if ($stateParams.termId === undefined) {
                Terms.some(function (obj) {
                    if (obj.current) {
                        $scope.Filter.term = obj.id;
                        $scope.SelectedTerm = obj;
                        return true;
                    }
                });
            }
            else {
                Terms.some(function (obj) {
                    if (obj.id == $stateParams.termId) {
                        $scope.Filter.term = obj.id;
                        $scope.SelectedTerm = obj;
                        return true;
                    }
                });
            }

            // Determine if filter box should be opened
            if ($stateParams.termId !== undefined) {
                $scope.showFilter = false;
                $scope.curriculumLoaded = true;
            }
            else
                $scope.showFilter = true;
        }

        $scope.toggleFilter = function () {
            $scope.showFilter = ($scope.showFilter === undefined || $scope.showFilter == false) ? true : false;
        }

        $scope.retrieveCurriculum = function () {
            $scope.fieldErrors = {};
            $state.go('subject_info.curriculum_load', { subjectId: $scope.Subject.id, termId: $scope.Filter.term });
        }
    }
]);




/*========== SUBJECT LESSON MATERIALS ==========*/
// SubjectLessonMaterialsController
studentSubjectApp.controller('SubjectLessonMaterialsController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject',
    'MyClassArm', '$state', 'LessonMaterials', '$timeout',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject, MyClassArm, $state, LessonMaterials, $timeout) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $scope.Subject = Subject;

            $rootScope.title = "Lesson Materials | Student";
            $rootScope.SubPageHeading = "Lesson Materials";
            $rootScope.activeView = "material";

            $scope.MyClassArm = MyClassArm;
            $scope.LessonMaterials = LessonMaterials;

            $scope.sortKey = "name";
            $scope.reverse = false;
            $scope.SearchResults = [];

            $scope.pagination = {
                PageSize: 10
            };
            $scope.currentPage = 1;
        }

        $scope.toggleFilter = function () {
            $scope.showFilter = ($scope.showFilter === undefined || $scope.showFilter == false) ? true : false;
        }

        $scope.retrieveMaterials = function () {
            $state.go('subject_info.lesson_materials', { subjectId: $scope.Subject.id });
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        }

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            var valid = ((row.title != null && angular.lowercase(row.title).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.content != null && angular.lowercase(row.content).indexOf(angular.lowercase($scope.searchText) || '') !== -1));
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember == null) $scope.SearchResults.push(row);
            }
            return valid;
        }

        $scope.downloadLectureMaterial = function ($index) {
            $index = (($scope.currentPage - 1) * $scope.pagination.PageSize) + $index;
            $scope.SelectedMaterial = $scope.LessonMaterials[$index];

            $timeout(function () {
                // download file
                $('#button_downloadLectureMaterial')[0].click();
            }, 0);
        }
    }
]);

// SubjectLessonMaterialViewController
studentSubjectApp.controller('SubjectLessonMaterialViewController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', '$state', 'SharedValues',
    'Subject', 'MyClassArm', 'LessonMaterial',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, $state, SharedValues, Subject, MyClassArm, LessonMaterial) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        var myDropzone = null;

        function setup() {
            $scope.Subject = Subject;
            $scope.MyClassArm = MyClassArm;

            $rootScope.title = "Lesson Material | Student";

            $rootScope.PageHeading = $scope.Subject.name;
            $rootScope.SubPageHeading = "View Lesson Material";

            $scope.LessonMaterial = LessonMaterial;

            $rootScope.hasLeftTab = false;

            $scope.Image = "";
            var parts = $scope.LessonMaterial.fileName.split(".");
            var extension = parts[parts.length - 1];
            if (extension == "pdf") $scope.Image = "/Images/pdf.png";
            else if (extension == "doc" || extension == "docx") $scope.Image = "/Images/word.png";
        }

        $rootScope.getParentUrl = function () {
            return $state.href('subject_info.lesson_materials', { subjectId: $scope.Subject.id });
        }
    }
]);




/*========== SUBJECT ASSIGNMENTS ==========*/
// SubjectAssignmentsController
studentSubjectApp.controller('SubjectAssignmentsController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject', '$stateParams',
    '$uibModal', 'MyClassArm', '$state', 'Assignments', 'StudentSubjectWebAPIService', '$timeout',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject, $stateParams, $uibModal, MyClassArm, $state, Assignments, StudentSubjectWebAPIService, $timeout) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $scope.Subject = Subject;
            $scope.StudentsList = [];

            $rootScope.title = "Assignments | Student";
            $rootScope.SubPageHeading = "Assignments";
            $rootScope.activeView = "assignment";

            $scope.MyClassArm = MyClassArm;
            $scope.Assignments = Assignments;

            $scope.sortKey = "name";
            $scope.reverse = false;
            $scope.SearchResults = [];

            $scope.pagination = {
                PageSize: 10
            };
            $scope.currentPage = 1;
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        }

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            var valid = ((row.title != null && angular.lowercase(row.title).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.question != null && angular.lowercase(row.question).indexOf(angular.lowercase($scope.searchText) || '') !== -1));
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember == null) $scope.SearchResults.push(row);
            }
            return valid;
        }

        $scope.downloadAssignment = function ($index) {
            $index = (($scope.currentPage - 1) * $scope.pagination.PageSize) + $index;
            $scope.SelectedAssignment = $scope.Assignments[$index];

            $timeout(function () {
                // download file
                $('#button_downloadAssignment')[0].click();
            }, 0);
        }

        $scope.answerSubmitted = function () {
            SharedMethods.createInfoToast("Answer has already been submitted for this assignment.");
        }

        $scope.deadlineMissed = function () {
            SharedMethods.createErrorToast("You have missed the deadline for this assignment.");
        }
    }
]);

// SubjectAssignmentViewController
studentSubjectApp.controller('SubjectAssignmentViewController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', '$state', 'SharedValues',
    'Subject', 'MyClassArm', 'Assignment',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, $state, SharedValues, Subject, MyClassArm, Assignment) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        var myDropzone = null;

        function setup() {
            $scope.Subject = Subject;
            $scope.MyClassArm = MyClassArm;

            $rootScope.title = "Assignment | Student";

            $rootScope.PageHeading = $scope.Subject.name;
            $rootScope.SubPageHeading = "View Assignment";

            $scope.Assignment = Assignment;

            $rootScope.hasLeftTab = false;

            $scope.Image = "";
            var parts = $scope.Assignment.fileName.split(".");
            var extension = parts[parts.length - 1];
            if (extension == "pdf") $scope.Image = "/Images/pdf.png";
            else if (extension == "doc" || extension == "docx") $scope.Image = "/Images/word.png";
        }

        $rootScope.getParentUrl = function () {
            return $state.href('subject_info.assignments', { subjectId: $scope.Subject.id });
        }
    }
]);

// SubmitSubjectAssignmentAnswerController
studentSubjectApp.controller('SubmitSubjectAssignmentAnswerController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'StudentSubjectWebAPIService', '$state',
    'Subject', 'MyClassArm', 'Assignment', 'SharedValues', '$timeout',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, StudentSubjectWebAPIService, $state, Subject, MyClassArm, Assignment, SharedValues, $timeout) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $rootScope.title = "Submit Answer | Student";
            $rootScope.PageHeading = Subject.name;
            $rootScope.SubPageHeading = "Assignments - Submit Answer";

            $scope.Subject = Subject;
            $scope.MyClassArm = MyClassArm;
            $scope.Assignment = Assignment;
            $scope.Answer = {
                answer: ""
            };

            $rootScope.hasLeftTab = false;

            $scope.customMenu = SharedValues.CustomMenu;

            // File handling
            var myDropzone = null;
            var previewTemplate = document.querySelector('#filePreviewTemplate').innerHTML;
            $timeout(function () {
                myDropzone = new Dropzone("div#myDropzone",
                {
                    url: "/post",
                    acceptedFiles: "image/*, application/pdf, application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document",
                    uploadMultiple: false,
                    maxFiles: 1,
                    clickable: true,
                    dictDefaultMessage: "Drag a file or just click to select <br/><p>Only one picture, word document or pdf file is allowed</p>",
                    thumbnailWidth: 200,
                    thumbnailHeight: 200,
                    previewTemplate: previewTemplate,
                    //maxFilesize: 2, //MB
                    autoProcessQueue: false,
                    addRemoveLinks: true
                });
                myDropzone.on("addedfile", function (file) {
                    var sameFile = null;
                    if ($scope.File != null && $scope.File.name == file.name && $scope.File.size == file.size) {
                        sameFile = file;
                        return true;
                    }
                    if (sameFile == null) {
                        $scope.File = file;
                        var fileType = file.type;
                        if (fileType == "application/pdf")
                            jQuery('.dz-image').html('<img data-dz-thumbnail="" src="/Images/pdf.png" width="200">');
                        else if (fileType == "application/msword" || fileType == "application/vnd.openxmlformats-officedocument.wordprocessingml.document")
                            jQuery('.dz-image').html('<img data-dz-thumbnail="" src="/Images/word.png" width="200">');
                    }
                    else {
                        //SharedMethods.createErrorToast("Only one file attachment is allowed");
                        myDropzone.removeFile(file);
                    }
                    $scope.fieldErrors = {};
                });
                myDropzone.on("removedfile", function (file) {
                    $scope.File = null;
                    $scope.fieldErrors = {};
                });
                myDropzone.on("error", function (file) {
                    if (!file.accepted) {
                        this.removeFile(file);
                    }
                });
            }, 0);
        }

        $rootScope.getParentUrl = function () {
            return $state.href('subject_info.assignments', { subjectId: $scope.Subject.id });
        };

        $scope.save = function () {
            var submitBtn = Ladda.create(document.querySelector('#button-insertAnswer'));
            submitBtn.start();
            $scope.processing = true;
            $scope.fieldErrors = {};

            var MyAnswer = {
                answer: encodeURIComponent($scope.Answer.answer),
                file: $scope.File,
                filename: $scope.File !== undefined && $scope.File != null ? $scope.File.name : "",
                subjectId: $scope.Subject.id,
                assignmentId: $scope.Assignment.id
            }

            var promiseSubmitAnswer = StudentSubjectWebAPIService.submitAssignmentAnswer(MyAnswer);
            promiseSubmitAnswer.then(function (pl) {
                $state.go('subject_info.assignments', { subjectId: $scope.Subject.id});

                //show success message
                SharedMethods.createSuccessToast("<strong>Answer</strong> was submitted successfully!");
            },
            function (error) {
                $scope.processing = false;
                submitBtn.stop();
                SharedMethods.showValidationErrors($scope, error, SharedMethods);
                //showLessonMaterialValidationErrors($scope, error, SharedMethods);
            });
        }
    }
]);




/*========== SUBJECT QUIZ ==========*/
// SubjectQuizController
studentSubjectApp.controller('SubjectQuizController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject',
    '$uibModal', 'MyClassArm', 'Quizzes', 'uibDateParser',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject, $uibModal, MyClassArm, Quizzes, uibDateParser) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $scope.Subject = Subject;

            $rootScope.title = "Quiz | Student";
            $rootScope.SubPageHeading = "Quiz";
            $rootScope.activeView = "quiz";

            $scope.MyClassArm = MyClassArm;
            $scope.Quizzes = Quizzes;
            var days = SharedMethods.days;
            var months = SharedMethods.monthNames;

            $scope.sortKey = "name";
            $scope.reverse = false;
            $scope.SearchResults = [];

            $scope.pagination = {
                PageSize: 10
            };
            $scope.currentPage = 1;
        }

        $scope.sort = function (keyname) {
            $scope.sortKey = keyname;   //set the sortKey to the param passed
            $scope.reverse = !$scope.reverse; //if true make it false and vice versa
        };

        $scope.$watch('searchText', function (val) {
            if (val !== undefined && val !== null) {
                $scope.SearchResults = [];
            }
        });

        $scope.search = function (row) {
            var valid = ((row.title != null && angular.lowercase(row.title).indexOf(angular.lowercase($scope.searchText) || '') !== -1)
                || (row.description != null && angular.lowercase(row.description).indexOf(angular.lowercase($scope.searchText) || '') !== -1));
            if (valid) {
                var foundMember = null;
                $scope.SearchResults.some(function (obj) {
                    if (obj.id == row.id) {
                        foundMember = obj;
                        return true;
                    }
                });
                if (foundMember == null) $scope.SearchResults.push(row);
            }
            return valid;
        };

        $scope.convertDuration = function (duration) {
            var hr = Math.floor(duration / 60);
            var min = duration % 60;

            return (hr > 0 ? hr + (hr === 1 ? "hr " : "hrs ") : "") + min + (min === 1 ? " min" : " mins");
        };

        $scope.deadlineMissed = function () {
            SharedMethods.createErrorToast("You have missed the deadline for this quiz.");
        };

        $scope.upcoming = function (Quiz) {
            var startDate = uibDateParser.parse(Quiz.startDate, 'yyyy-MM-ddTHH:mm:ss');
            SharedMethods.createInfoToast("This quiz will be available from " + SharedMethods.dateToMyString(startDate) + " by " +
                (startDate.getHours() === 0 ? 12 : startDate.getHours() % 12)
                + ":" + (startDate.getMinutes() < 10 ? '0' : '') + startDate.getMinutes()
                + " " + (startDate.getHours() > 12 ? "PM" : "AM"));
        };

        $scope.startQuiz = function (Quiz) {
            // var Quiz = $scope.Quizzes[$index];
            if (Quiz.upcoming) {
                var date = Quiz.date;
                if (date !== null && typeof Quiz.date === 'string') {
                    var dateStr = date.substr(0, date.indexOf('T'));
                    date = uibDateParser.parse(dateStr, 'yyyy-MM-dd');
                }
                SharedMethods.createInfoToast("This quiz will be available on <b>" + dateToMyString(date) + "</b>.");
            }
            else if (Quiz.expired) {
                SharedMethods.createErrorToast("You have missed the deadline for this quiz.");
            }
            else {
                // Check if token is required and valid
                if (Quiz.tokenRequired && !Quiz.tokenValid) {
                    // Show simple token input modal
                    var tokenModalInstance = $uibModal.open({
                        template: `
                            <div class="modal-header">
                                <button type="button" class="close" ng-click="$dismiss()" aria-label="Close">
                                    <span aria-hidden="true">&times;</span>
                                </button>
                                <h4 class="modal-title">
                                    <i class="fa fa-key"></i> Enter Quiz Access Token
                                </h4>
                            </div>
                            <div class="modal-body">
                                <div class="text-center" style="margin-bottom: 20px;">
                                    <i class="fa fa-shield fa-3x text-primary"></i>
                                    <h4 style="margin-top: 15px;">Secure Quiz Access</h4>
                                    <p class="text-muted">This quiz requires a 6-digit access token.</p>
                                </div>

                                <div class="alert alert-info">
                                    <strong>Quiz:</strong> {{quiz.title}}<br>
                                    <strong>Subject:</strong> {{subject.name}}
                                </div>

                                <form name="tokenForm" ng-submit="submitToken()" novalidate>
                                    <div class="form-group">
                                        <label for="tokenInput" class="control-label">
                                            <i class="fa fa-key"></i> Access Token
                                        </label>
                                        <input type="text"
                                               class="form-control text-center"
                                               id="tokenInput"
                                               ng-model="token"
                                               placeholder="Enter 6-digit token"
                                               maxlength="6"
                                               required
                                               style="font-family: monospace; font-size: 18px; font-weight: bold; letter-spacing: 3px;">
                                    </div>

                                    <div class="alert alert-danger" ng-if="error">
                                        <i class="fa fa-exclamation-triangle"></i> {{error}}
                                    </div>

                                    <div class="alert alert-success" ng-if="success">
                                        <i class="fa fa-check-circle"></i> {{success}}
                                    </div>

                                    <div class="text-center" ng-if="validating">
                                        <i class="fa fa-spinner fa-spin"></i> Validating token...
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-default" ng-click="$dismiss()">Cancel</button>
                                <button type="button"
                                        class="btn btn-primary"
                                        ng-click="submitToken()"
                                        ng-disabled="!token || token.length !== 6 || validating">
                                    <i class="fa fa-unlock" ng-if="!validating"></i>
                                    <i class="fa fa-spinner fa-spin" ng-if="validating"></i>
                                    {{validating ? 'Validating...' : 'Access Quiz'}}
                                </button>
                            </div>
                        `,
                        controller: function($scope, $uibModalInstance) {
                            console.log('Token modal controller initialized');

                            $scope.quiz = Quiz;
                            $scope.subject = $scope.Subject;
                            $scope.token = '';
                            $scope.error = null;
                            $scope.success = null;
                            $scope.validating = false;

                            $scope.submitToken = function() {
                                console.log('submitToken called with token:', $scope.token);

                                if (!$scope.token || $scope.token.length !== 6) {
                                    console.log('Token validation failed - invalid length or empty');
                                    $scope.error = 'Please enter a valid 6-digit token';
                                    return;
                                }

                                $scope.validating = true;
                                $scope.error = null;
                                $scope.success = null;

                                var tokenRequest = {
                                    Token: $scope.token,
                                    QuizId: Quiz.id,
                                    SubjectId: $scope.Subject.id
                                };

                                console.log('Sending token validation request:', tokenRequest);

                                // Use the service from the parent scope
                                StudentSubjectWebAPIService.validateQuizToken(tokenRequest).then(function (response) {
                                    console.log('Token validation response:', response);
                                    $scope.validating = false;

                                    if (response.data.IsValid) {
                                        $scope.success = response.data.Message;
                                        Quiz.tokenValid = true;

                                        setTimeout(function() {
                                            $uibModalInstance.close(true);
                                        }, 1000);
                                    } else {
                                        $scope.error = response.data.Message || 'Invalid token. Please try again.';
                                    }
                                }, function (error) {
                                    console.error('Token validation error:', error);
                                    $scope.validating = false;
                                    $scope.error = 'Failed to validate token. Please try again.';
                                });
                            };
                        },
                        openedClass: 'myModalBody',
                        size: 'md',
                        backdrop: 'static',
                        keyboard: false
                    });

                    tokenModalInstance.result.then(function (tokenValidated) {
                        if (tokenValidated) {
                            // Token validated successfully, proceed to start quiz
                            proceedToStartQuiz(Quiz);
                        }
                    }, function () {
                        // Modal dismissed, do nothing
                    });
                } else {
                    // No token required or token already valid, proceed directly
                    proceedToStartQuiz(Quiz);
                }
            }
        };

        // Helper function to proceed with quiz start
        function proceedToStartQuiz(Quiz) {
            var ModalModel = {
                HeaderContent: "Start Quiz",
                BodyMessage: "Start the \"" + Quiz.title + "\" for " + $scope.Subject.name + ".<br/>"
                    + "<label class='red no-margin-bottom margin-top-one'>Warning: Do not attempt to close this quiz after it has started!!</label>",
                ButtonText: "Start",
                ButtonClass: "btn-primary"
            };

            var QuizModel = {
                Quiz: Quiz,
                SubjectId: $scope.Subject.id
            };
            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'QuizStartController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    },
                    QuizModel: function () {
                        return QuizModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        }
    }
]);

// QuizStartController
studentSubjectApp.controller('QuizStartController', ['$scope', '$uibModalInstance', 'StudentSubjectWebAPIService', 'ModalModel', 'QuizModel', 'SharedMethods', '$state',
    '$cookies',
    function ($scope, $uibModalInstance, StudentSubjectWebAPIService, ModalModel, QuizModel, SharedMethods, $state, $cookies) {

        $scope.ModalModel = ModalModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            SharedMethods.keepModalOpen($scope);

            var promiseGetQuizQuestionDetails = StudentSubjectWebAPIService.getQuizQuestionDetails(QuizModel.Quiz.id);
            promiseGetQuizQuestionDetails.then(function (pl) {
                if (pl.data === "upcoming") {
                    var date = QuizModel.Quiz.date;
                    if (date !== null && typeof QuizModel.Quiz.date === 'string') {
                        var dateStr = date.substr(0, date.indexOf('T'));
                        date = uibDateParser.parse(dateStr, 'yyyy-MM-dd');
                    }
                    SharedMethods.createInfoToast("This quiz will be available on <b>" + dateToMyString(date) + "</b>.");
                }
                else if (pl.data === "expired")
                    SharedMethods.createErrorToast("You have missed the deadline for this quiz.");
                else if (pl.data === "done")
                    SharedMethods.createInfoToast("You have already attempted this quiz.");
                else {
                    // save in a cookie
                    var sequence = pl.data;
                    var model = {
                        questions: sequence,
                        subjectId: QuizModel.SubjectId,
                        quizId: QuizModel.Quiz.id
                    };
                    SharedMethods.localSetWithExpiry("questionSequence", JSON.stringify(model), 60);
                    localStorage.removeItem("currentCountDownTime");
                    // var expiryDate = new Date();
                    // expiryDate.setMinutes(new Date().getMinutes() + 60);
                    // $cookies.put("questionSequence", JSON.stringify(model), { expires: expiryDate, domain: document.domain });
                    // $cookies.put("currentCountDownTime", "", { expires: new Date(), domain: document.domain });

                    //redirect to question page
                    $state.go('start_quiz', { subjectId: QuizModel.SubjectId, quizId: QuizModel.Quiz.id, questionIndex: 1 }, { reload: true });
                }

                // release modal
                SharedMethods.releaseModal($scope);

                //dismiss modal
                $uibModalInstance.close();
            },
                function (error) {
                    //dismiss modal
                    $uibModalInstance.dismiss('cancel');
                    //show error message
                    SharedMethods.createErrorToast('Problem starting <strong>Quiz</strong>. Try again!');
                });
        };
    }
]);

function dateToMyString(date) {
    return days[date.getDay()] + " " + monthNames[date.getMonth()] + " " + date.getDate() + ", " + date.getFullYear();
}

// QuizStartedQuestionController
studentSubjectApp.controller('QuizStartedQuestionController', ['$scope', '$rootScope', 'SharedMethods', 'RedirectInfo', 'Subject', '$stateParams',
    '$uibModal', 'MyClassArm', '$state', 'Quiz', 'Question', 'StudentSubjectWebAPIService', '$cookies',
    function ($scope, $rootScope, SharedMethods, RedirectInfo, Subject, $stateParams, $uibModal, MyClassArm, $state, Quiz, Question, StudentSubjectWebAPIService, $cookies) {
        if (!SharedMethods.redirect(RedirectInfo)) {
            setup();
        }

        function setup() {
            $scope.Subject = Subject;
            $scope.MyClassArm = MyClassArm;
            $scope.Quiz = Quiz;

            $rootScope.Other = true;
            $rootScope.QuizStartedPage = true;
            $rootScope.hasLeftTab = false;
            $rootScope.showBack = false;

            $rootScope.title = "Quiz | Student";
            $rootScope.PageHeading = "";
            $rootScope.SubPageHeading = "";

            $scope.QuestionIndex = $stateParams.questionIndex;
            $scope.Question = Question;
            $scope.AttemptedCount = 0;
            $scope.finishing = false;

            var questionSequence = SharedMethods.localGetWithExpiry("questionSequence");
            // var questionSequence = $cookies.get("questionSequence") !== undefined ? $cookies.get("questionSequence") : null;
            if (questionSequence !== null) {
                $scope.QuestionList = JSON.parse(questionSequence).questions;
                for (var index in $scope.QuestionList) {
                    if ($scope.QuestionList[index].attempted)
                        $scope.AttemptedCount++;
                }
                if ($scope.QuestionList[parseInt($scope.QuestionIndex) - 1].attempted)
                    $scope.Question.answer = $scope.QuestionList[parseInt($scope.QuestionIndex) - 1].answer;
            }
            else {
                $state.go('subject_info.quiz', { subjectId: $scope.Subject.id }, { reload: true });
            }

            var currentCountDownTime = SharedMethods.localGetWithExpiry("currentCountDownTime");
            // var currentCountDownTime = $cookies.get("currentCountDownTime") !== undefined ? $cookies.get("currentCountDownTime") : null;
            if (currentCountDownTime !== null) {
                $rootScope.CountDownTime = parseInt(currentCountDownTime);
                if ($rootScope.CountDownTime === 0 && !scope.finishing) $scope.timeUp();
            } else {
                $rootScope.CountDownTime = $scope.Quiz.duration * 60;
            }
            $scope.EntryCountDownTime = $rootScope.CountDownTime;
        }

        $rootScope.$on('timer-tick', function (event, args) {
            $scope.CurrentCountDownTime = args.millis / 1000;
            SharedMethods.localSetWithExpiry("currentCountDownTime", $scope.CurrentCountDownTime, 10);
            // var expiryDate = new Date();
            // expiryDate.setMinutes(new Date().getMinutes() + 10);
            // $cookies.put("currentCountDownTime", $scope.CurrentCountDownTime, { expires: expiryDate, domain: document.domain });

            if ($scope.EntryCountDownTime !== undefined) {
                if (($scope.EntryCountDownTime - $scope.CurrentCountDownTime) >= 600) {
                    $scope.EntryCountDownTime = $scope.CurrentCountDownTime;

                    // get questions list
                    var questionSequence = SharedMethods.localGetWithExpiry("questionSequence");
                    // var questionSequence = $cookies.get("questionSequence") !== undefined ? $cookies.get("questionSequence") : null;
                    $scope.QuestionList = JSON.parse(questionSequence).questions;

                    var model = {
                        questions: $scope.QuestionList,
                        subjectId: $scope.Subject.id,
                        quizId: $scope.Quiz.id
                    };
                    SharedMethods.localSetWithExpiry("questionSequence", JSON.stringify(model), 60);
                    // expiryDate = new Date();
                    // expiryDate.setMinutes(new Date().getMinutes() + 60);
                    // $cookies.put("questionSequence", JSON.stringify(model), { expires: expiryDate, domain: document.domain });
                }
            }
        });

        $scope.gotoPreviousQuestion = function () {
            if ($scope.QuestionIndex != 1) {
                var qIndex = parseInt($scope.QuestionIndex) - 1;
                $scope.gotoQuestion(qIndex);
            }
        }

        $scope.gotoNextQuestion = function () {
            if ($scope.QuestionIndex < $scope.Quiz.questionCount) {
                var qIndex = parseInt($scope.QuestionIndex) + 1;
                $scope.gotoQuestion(qIndex);
            }
        };

        $scope.gotoQuestion = function (questionIndex) {
            if ($scope.Question.answer !== "") {
                $scope.QuestionList[parseInt($scope.QuestionIndex) - 1].answer = $scope.Question.answer;
                $scope.QuestionList[parseInt($scope.QuestionIndex) - 1].attempted = true;
            }
            var model = {
                questions: $scope.QuestionList,
                subjectId: $scope.Subject.id,
                quizId: $scope.Quiz.id
            };
            SharedMethods.localSetWithExpiry("questionSequence", JSON.stringify(model), 60);
            // var expiryDate = new Date();
            // expiryDate.setMinutes(new Date().getMinutes() + 60);
            // $cookies.put("questionSequence", JSON.stringify(model), { expires: expiryDate, domain: document.domain });

            // go to question
            $state.go('start_quiz', { subjectId: $scope.Subject.id, quizId: $scope.Quiz.id, questionIndex: questionIndex }, { reload: false });
        };

        $rootScope.finishQuiz = function () {
            // confirm submission
            var ModalModel = {
                HeaderContent: "FINISH QUIZ",
                BodyMessage: "Are you sure you have completed all the questions of the quiz?",
                ButtonText: "Finish",
                ButtonClass: "btn-primary"
            };

            var QuizModel = {
                Quiz: $scope.Quiz,
                SubjectId: $scope.Subject.id,
                Question: $scope.Question,
                QuestionIndex: $scope.QuestionIndex
            };

            var modalInstance = $uibModal.open({
                templateUrl: 'Modal/Confirm.html',
                controller: 'FinishQuizController',
                openedClass: 'myModalBody',
                resolve: {
                    ModalModel: function () {
                        return ModalModel;
                    },
                    QuizModel: function () {
                        return QuizModel;
                    }
                }
            });
            modalInstance.opened.then(function () {
            });
            modalInstance.result.then(function () {
            }, function () {
            });
        };

        $rootScope.timeUp = function () {
            $scope.finishing = true;
            $rootScope.loadingPopUpView = true;

            // get questions list
            var questionSequence = SharedMethods.localGetWithExpiry("questionSequence");
            // var questionSequence = $cookies.get("questionSequence") !== undefined ? $cookies.get("questionSequence") : null;
            $scope.QuestionList = JSON.parse(questionSequence).questions;
            if ($scope.Question.answer !== "") {
                $scope.QuestionList[parseInt($scope.QuestionIndex) - 1].answer = $scope.Question.answer;
                $scope.QuestionList[parseInt($scope.QuestionIndex) - 1].attempted = true;
            }

            // submit questions and answer (with subject and quiz) for scoring
            var model = {
                questions: $scope.QuestionList,
                subjectId: $scope.Subject.id,
                quizId: $scope.Quiz.id
            };

            // try submit
            var attempts = 0;
            var done = submitQuiz(model, StudentSubjectWebAPIService, $cookies, $rootScope, $scope, $uibModal);
            while (!done && attempts < 10) {
                done = submitQuiz(model, StudentSubjectWebAPIService, $cookies, $rootScope, $scope, $uibModal);
                attempts = attempts + 1;
            }
            if (!done) {
                $scope.finishing = false;
                SharedMethods.createErrorToast('Problem submitting <strong>Quiz</strong>. Try again!');
            }
            
        };
    }
]);

function submitQuiz(model, StudentSubjectWebAPIService, $cookies, $rootScope, $scope, $uibModal) {
    var promiseSubmitQuiz = StudentSubjectWebAPIService.submitQuiz(model);
    promiseSubmitQuiz.then(function (pl) {

        // remove cookies
        localStorage.removeItem("questionSequence");
        localStorage.removeItem("currentCountDownTime");
        // var expiryDate = new Date();
        // $cookies.put("questionSequence", "", { expires: expiryDate, domain: document.domain });
        // $cookies.put("currentCountDownTime", "", { expires: expiryDate, domain: document.domain });

        // show score (and allow for checking correction)
        showScore(pl.data, $scope.Quiz.questionCount, $scope.Subject.id, $uibModal, $rootScope);
        return true;
    },
    function (error) {
        $rootScope.loadingPopUpView = false;

        //show error message
        // SharedMethods.createErrorToast('Problem submitting <strong>Quiz</strong>. Try again!');
        return false;
    });
}

// FinishQuizController
studentSubjectApp.controller('FinishQuizController', ['$scope', '$uibModalInstance', 'StudentSubjectWebAPIService', 'ModalModel', 'QuizModel', 'SharedMethods',
    '$rootScope', '$cookies', '$uibModal',
    function ($scope, $uibModalInstance, StudentSubjectWebAPIService, ModalModel, QuizModel, SharedMethods, $rootScope, $cookies, $uibModal) {

        $scope.ModalModel = ModalModel;
        $scope.QuizModel = QuizModel;

        $scope.close = function () {
            $uibModalInstance.dismiss('cancel');
        };

        $scope.confirm = function () {
            // stop timer
            $rootScope.$broadcast('timer-stop');

            // get questions list
            var questionSequence = SharedMethods.localGetWithExpiry("questionSequence");
            // var questionSequence = $cookies.get("questionSequence") !== undefined ? $cookies.get("questionSequence") : null;
            $scope.QuestionList = JSON.parse(questionSequence).questions;
            if ($scope.QuizModel.Question.answer !== "") {
                $scope.QuestionList[parseInt($scope.QuizModel.QuestionIndex) - 1].answer = $scope.QuizModel.Question.answer;
                $scope.QuestionList[parseInt($scope.QuizModel.QuestionIndex) - 1].attempted = true;
            }

            // submit questions and answer (with subject and quiz) for scoring
            SharedMethods.keepModalOpen($scope);

            var model = {
                questions: $scope.QuestionList,
                subjectId: $scope.QuizModel.SubjectId,
                quizId: $scope.QuizModel.Quiz.id
            };
            var promiseSubmitQuiz = StudentSubjectWebAPIService.submitQuiz(model);
            promiseSubmitQuiz.then(function (pl) {

                // release modal
                SharedMethods.releaseModal($scope);

                //dismiss modal
                $uibModalInstance.close();

                // remove cookies
                localStorage.removeItem("questionSequence");
                localStorage.removeItem("currentCountDownTime");
                // var expiryDate = new Date();
                // $cookies.put("questionSequence", "", { expires: expiryDate, domain: document.domain });
                // $cookies.put("currentCountDownTime", "", { expires: expiryDate, domain: document.domain });

                // show score (and allow for checking correction)
                showScore(pl.data, $scope.QuizModel.Quiz.questionCount, $scope.QuizModel.SubjectId, $uibModal, $rootScope);
            },
                function (error) {
                    // release modal
                    SharedMethods.releaseModal($scope);

                    //dismiss modal
                    //$uibModalInstance.dismiss('cancel');

                    //show error message
                    SharedMethods.createErrorToast('Problem submitting <strong>Quiz</strong>. Try again!');
                });
        };
    }
]);



function showScore(score, overall, subjectId, $uibModal, $rootScope) {
    var ModalModel = {
        Score: score,
        Overall: overall
    };

    var QuizModel = {
        SubjectId: subjectId,
    }

    var modalInstance = $uibModal.open({
        templateUrl: 'Modal/ScoreCanvas.html',
        controller: 'ScoreCanvasController',
        openedClass: 'myModalBody',
        resolve: {
            ModalModel: function () {
                return ModalModel;
            },
            QuizModel: function () {
                return QuizModel;
            }
        }
    });
    modalInstance.opened.then(function () {
        $rootScope.loadingPopUpView = false;
    });
    modalInstance.result.then(function () {
    }, function () {
    });
}

// ScoreCanvasController
studentSubjectApp.controller('ScoreCanvasController', ['$scope', '$uibModalInstance', 'StudentSubjectWebAPIService', 'ModalModel', 'QuizModel', 'SharedMethods', '$state',
    function ($scope, $uibModalInstance, StudentSubjectWebAPIService, ModalModel, QuizModel, SharedMethods, $state) {

        $scope.ModalModel = ModalModel;

        $scope.confirm = function () {
            $state.go('subject_info.quiz', { subjectId: QuizModel.SubjectId}, { reload: true });
        }
    }
]);





// Error404Controller
studentSubjectApp.controller('Error404Controller', ['$scope', '$rootScope',
    function ($scope, $rootScope) {
        $rootScope.title = "Page Not Found | School Cater";
        $rootScope.activePage = "";
        $rootScope.removeHeadSection = true;
    }]);