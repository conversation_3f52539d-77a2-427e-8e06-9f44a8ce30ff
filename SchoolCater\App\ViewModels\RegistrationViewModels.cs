﻿using SchoolCater.Models;
using System;
using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Web;

namespace SchoolCater.App.ViewModels
{

    public class SchoolBasicProfileViewModel
    {
        public SchoolBasicProfileViewModel(School sch)
        {
            Id = sch.Id;
            Name = sch.Name;
            CatchPhrase = sch.CatchPhrase;
            Address = sch.Address;
        }

        public int Id { get; set; }
        public string Name { get; set; }
        public string Address { get; set; }
        public string CatchPhrase { get; set; }
    }

    public class SchoolViewModel
    {
        public SchoolViewModel() { }

        public SchoolViewModel(School sch, RegNumberSetting regNumSetting)
        {
            Id = sch.Id;
            Name = sch.Name;
            CatchPhrase = sch.CatchPhrase;
            Email = sch.Email;
            Phone = sch.Phone;
            Address = sch.Address;
            SmsHeader = sch.SmsHeader;
            Bank = sch.Bank;
            AccountName = sch.AccountName;
            AccountNumber = sch.AccountNumber;
            HasLogo = sch.Logo != null;

            AutoGenerateRegNo = sch.AutoGenerateRegNo.ToString().ToLower();
            RegNoPrefix = regNumSetting != null && !string.IsNullOrWhiteSpace(regNumSetting.Prefix) ? regNumSetting.Prefix : "";
            SlashAfterPrefix = regNumSetting != null && regNumSetting.SlashAfterPrefix != null ? regNumSetting.SlashAfterPrefix.ToString().ToLower() : "false";
            RegNumberYear = regNumSetting != null && regNumSetting.Year != null ? regNumSetting.Year.ToString().ToLower() : "false";
            RegNumberYearLength = regNumSetting != null && regNumSetting.YearLength != null ? regNumSetting.YearLength.Value : 4;
            SlashAfterYear = regNumSetting != null && regNumSetting.SlashAfterYear != null ? regNumSetting.SlashAfterYear.ToString().ToLower() : "false";
            RegNoMinNumLength = regNumSetting != null ? regNumSetting.MinNumberLength : 3;
            SlashAfterNumber = regNumSetting != null && regNumSetting.SlashAfterNumber != null ? regNumSetting.SlashAfterNumber.ToString().ToLower() : "false";
            RegNoSuffix = regNumSetting != null && !string.IsNullOrWhiteSpace(regNumSetting.Suffix) ? regNumSetting.Suffix : "";
        }

        public byte[] Logo { get; set; }
        public bool HasLogo { get; set; }

        public int Id { get; set; }

        [Required(ErrorMessage = "Enter the name of the school.")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Enter the motto of the school.")]
        public string CatchPhrase { get; set; }

        [Required(ErrorMessage = "Enter the address of the school.")]
        public string Address { get; set; }

        [Required(ErrorMessage = "Enter the phone number of the school.")]
        [Phone(ErrorMessage = "Enter a valid phone number.")]
        public string Phone { get; set; }

        [Required(ErrorMessage = "Enter the email address of the school.")]
        [DataType(DataType.EmailAddress)]
        [EmailAddress(ErrorMessage = "Enter a valid email address.")]
        public string Email { get; set; }

        [Required(ErrorMessage="Enter the sender name shown when sending sms.")]
        [RegularExpression("^[a-zA-Z ]*$", ErrorMessage = "Only use letters from A to Z")]
        public string SmsHeader { get; set; }

        public string Bank { get; set; }

        public string AccountName { get; set; }

        [RegularExpression("([0-9]+)", ErrorMessage="Please enter only digits.")]
        [MaxLength(10, ErrorMessage = "The account number should be 10 digits."), MinLength(10, ErrorMessage = "The account number should be 10 digits.")]
        public string AccountNumber { get; set; }


        // REGISTRATION NUMBER
        [Required(ErrorMessage = "Please select an option.")]
        public string AutoGenerateRegNo { get; set; }

        public string RegNoPrefix { get; set; }

        public string SlashAfterPrefix { get; set; }

        public string RegNumberYear { get; set; }

        public int? RegNumberYearLength { get; set; }

        public string SlashAfterYear { get; set; }

        public int RegNoMinNumLength { get; set; }

        public string SlashAfterNumber { get; set; }

        public string RegNoSuffix { get; set; }
    }

    public class GroupViewModel
    {
        public int Id { get; set; }
        public byte[] Logo { get; set; }

        [Required(ErrorMessage = "Enter the name of the group.")]
        public string Name { get; set; }

        [Required(ErrorMessage = "Enter the phone number of the group.")]
        [Phone(ErrorMessage = "Enter a valid phone number.")]
        public string Phone { get; set; }

        [Required(ErrorMessage = "Enter the email address of the group.")]
        [EmailAddress(ErrorMessage = "Enter a valid email address.")]
        public string Email { get; set; }
        public bool HasLogo { get; set; }
    }

    public class UserBaseViewModel
    {
        public int Id { get; set; }

        [Required(ErrorMessage = "Enter a last name.")]
        [Display(Name = "Last Name")]
        public string LastName { get; set; }

        [Required(ErrorMessage = "Enter a first name.")]
        [Display(Name = "First Name")]
        public string FirstName { get; set; }

        [Display(Name = "Middle Name")]
        public string MiddleName { get; set; }

        [EmailAddress(ErrorMessage = "Enter a valid email address")]
        public virtual string Email { get; set; }

        [Phone(ErrorMessage = "Enter a valid phone number.")]
        public virtual string Phone { get; set; }

        public string Address { get; set; }

        public byte[] Picture { get; set; }

        public virtual string Gender { get; set; }

        public string Password { get; set; }
    }

    public class ProprietorViewModel : UserBaseViewModel
    {

        [Required(ErrorMessage="Please select a title.")]
        public string Title { get; set; }

        [EmailAddress(ErrorMessage = "Enter a valid email address.")]
        [Required(ErrorMessage="Enter an email address.")]
        public override string Email { get; set; }

        [Phone(ErrorMessage = "Enter a valid phone number.")]
        [Required(ErrorMessage = "Enter a phone number.")]
        public override string Phone { get; set; }

        //public bool IsProprietor { get; set; }

        public School School { get; set; }

        public GroupSchool GroupSchool { get; set; }
    }

    public class AffiliateMarketerRegisterViewModel
    {
        [Required(ErrorMessage = "Enter your last name.")]
        public string LastName { get; set; }

        [Required(ErrorMessage = "Enter your first name.")]
        public string FirstName { get; set; }

        [Required(ErrorMessage = "Enter your email address.")]
        [EmailAddress(ErrorMessage = "Enter a valid email address.")]
        public virtual string Email { get; set; }

        [Required(ErrorMessage = "Enter your password.")]
        [MinLength(6, ErrorMessage = "Password must be atleast 6 characters.")]
        public string Password { get; set; }
    }
}