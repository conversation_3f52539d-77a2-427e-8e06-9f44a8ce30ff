﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Mvc;
using System.Web.Routing;

namespace SchoolCater
{
    public class RouteConfig
    {
        public static void RegisterRoutes(RouteCollection routes)
        {
            routes.IgnoreRoute("{resource}.axd/{*pathInfo}");

            routes.LowercaseUrls = true;

            //For Attribute routing
            routes.MapMvcAttributeRoutes();

            AreaRegistration.RegisterAllAreas();

            /*routes.MapRoute(
                name: "Default",
                url: "{controller}/{action}/{id}",
                defaults: new { controller = "Home", action = "Index", id = UrlParameter.Optional }
            );*/

            routes.MapRoute(
                name: "Account",
                url: "Account/{*.}",
                defaults: new { controller = "Account", action = "Index" },
               namespaces: new[] { "SchoolCater.Controllers" }
            );

            routes.MapRoute(
               name: "Default",
               url: "{*anything}",
               defaults: new { controller = "Home", action = "Index" },
               namespaces: new[] { "SchoolCater.Controllers" }
           );
        }
    }
}
