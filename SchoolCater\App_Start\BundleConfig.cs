﻿using System.Web.Optimization;

namespace SchoolCater
{
    public class BundleConfig
    {
        // For more information on bundling, visit http://go.microsoft.com/fwlink/?LinkId=301862
        public static void RegisterBundles(BundleCollection bundles)
        {
            /*========== STYLE BUNDLES ==========*/
            bundles.Add(new StyleBundle("~/Content/css").Include(
                    "~/Content/reset-1.6.1.css",
                    "~/Content/bootstrap/css/bootstrap.css",
                    //"~/Content/supersized.css",
                    //"~/Content/supersized.shutter.css",
                    "~/Content/fontawesome-5.3.1.min.css",
                    "~/Content/magnific-popup.css",
                    "~/Content/owl-carousel/css/owl.carousel.min.css",
                    "~/Content/owl-carousel/css/owl.theme.default.min.css",
                    "~/Content/elegant-font.css",
                    "~/Content/preloader.css",
                    "~/Content/style.css",
                    "~/Content/animate.css",
                    "~/Content/superfish.css",
                    "~/Content/solid_color/deepblue.css",
                    "~/Content/color/deepblue.css",
                    "~/Content/intlTelInput.css",
                    "~/Content/ladda.css",
                    "~/Content/myanimate.css",
                    "~/Content/ngToast.css",
                    "~/Content/ngToast-animations.css",
                    "~/Content/tooltip/tooltipster.css",
                    "~/Content/tooltip/themes/tooltipster-me.css",
                    "~/Content/print.css",
                    "~/Content/responsive.css"));

            bundles.Add(new StyleBundle("~/Content/account-css").Include(
                    "~/Content/style_account.css"));

            bundles.Add(new StyleBundle("~/Content/css_admin").Include(
                    "~/Content/reset-1.6.1.css",
                    "~/Content/bootstrap/css/bootstrap.css",
                    "~/Content/dropzone.min.css",
                    "~/Content/fontawesome-5.3.1.min.css",
                    //"~/Content/fontawesome-5.3.1/css/all.css",
                    "~/Content/magnific-popup.css",
                    "~/Content/owl.carousel.css",
                    "~/Content/elegant-font.css",
                    "~/Content/preloader.css",
                    "~/Content/style.css",
                    "~/Content/style_user.css",
                    "~/Content/superfish.css",
                    "~/Content/solid_color/deepblue.css",
                    "~/Content/color/deepblue.css",
                    "~/Content/responsive.css",
                    "~/Content/ladda.css",
                    "~/Content/pop.css",
                    "~/Content/table.css",
                    "~/Content/datepicker.css",
                    "~/Content/datepicker2.css",
                    "~/Content/upload.css",
                    "~/Content/jquery.fileupload.css",
                    "~/Content/icons/themify-icons/themify-icons.css",
                    "~/Content/intlTelInput.css",
                    "~/Content/tooltip/tooltipster.css",
                    "~/Content/tooltip/themes/tooltipster-me.css",
                    "~/Content/tooltip/themes/tooltipster-me2.css",
                    "~/Content/print.css",
                    "~/Content/myanimate.css",
                    "~/ChatJs/Styles/jquery.chatjs.css",
                    "~/Content/ngToast.css",
                    "~/Content/ngToast-animations.css"));



            /*========== SCRIPT BUNDLES ==========*/

            bundles.Add(new ScriptBundle("~/bundles/jquery").Include(
                        "~/Scripts/jquery/jquery-1.11.2.min.js"));
            //"~/Scripts/jquery-{version}.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/jqueryval").Include(
                        "~/Scripts/jquery/jquery.validate*"));

            // Use the development version of Modernizr to develop with and learn from. Then, when you're
            // ready for production, use the build tool at http://modernizr.com to pick only the tests you need.
            bundles.Add(new ScriptBundle("~/bundles/modernizr").Include(
                        "~/Scripts/modernizr-*"));

            bundles.Add(new ScriptBundle("~/bundles/home").Include(
                     "~/Scripts/modernizr.js",
                     "~/Scripts/device.min.js",
                    "~/Scripts/jquery/jquery.tooltipster.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/home2").Include(
                "~/Content/bootstrap/js/bootstrap.min.js",
                //"~/Scripts/supersized.3.2.7.min.js",
                //"~/Scripts/supersized.shutter.min.js",
                "~/Scripts/jquery/jquery.easing.1.3.js",
                "~/Scripts/jquery/jquery.scrollTo.min.js",
                "~/Content/owl-carousel/owl.carousel.min.js",
                "~/Scripts/waypoints.min.js",
                "~/Scripts/jquery/jquery.magnific-popup.min.js",
                "~/Scripts/jquery/jquery.hoverdir.js",
                "~/Scripts/intlTelInput.js",
                "~/Scripts/spin.js",
                "~/Scripts/ladda.js",
                "~/Scripts/hoverIntent.js",
                "~/Scripts/superfish.js"));

            bundles.Add(new ScriptBundle("~/bundles/admin").Include(
                     "~/Scripts/css-pop.js",
                     "~/Scripts/jquery/jquery.magnific-popup.min.js",
                     "~/Scripts/jquery/jquery.noty.packaged.min.js",
                     "~/Scripts/tinymce/tinymce.min.js"));

            bundles.Add(new ScriptBundle("~/bundles/admin2").Include(
                "~/Content/bootstrap/js/bootstrap.min.js",
                "~/Scripts/dropzone.min.js",
                "~/Scripts/hoverIntent.js",
                "~/Scripts/intlTelInput.js",
                "~/Scripts/jquery/jquery.nicescroll.min.js",
                "~/Scripts/jquery/jquery-ui-1.10.3.custom.min.js",
                "~/Scripts/jquery/jquery.fileupload.js",
                "~/Scripts/jquery/jquery.iframe-transport.js",
                "~/Scripts/spin.js",
                "~/Scripts/ladda.js",
                "~/Scripts/owl.carousel.min.js",
                "~/Scripts/slidebars.min.js",
                "~/Scripts/superfish.js",
                "~/Scripts/moment.js"));

            bundles.Add(new ScriptBundle("~/bundles/chat").Include(
                    "~/Scripts/jquery/jquery.signalR-2.0.3.min.js",
                    "~/signalr/hubs",
                    "~/ChatJs/Scripts/jquery.chatjs.signalradapter.js",
                    "~/ChatJs/Scripts/jquery.autosize.min.js",
                    "~/ChatJs/Scripts/jquery.chatjs.js"));

            bundles.Add(new Bundle("~/bundles/angularjs").Include(
                    "~/Scripts/angular/angularjs/angular.js",
                    "~/Scripts/angular/angularjs/angular-route.js",
                    "~/Scripts/angular/angularjs/angular-ui-router.js",
                    "~/Scripts/angular/angularjs/angular-animate.js",
                    "~/Scripts/angular/angularjs/angular-breadcrumb.min.js",
                    "~/Scripts/angular/angularjs/angular-sanitize.js"));

            bundles.Add(new Bundle("~/bundles/angular-ui").Include(
                        "~/Scripts/angular/angular-ui/ui-bootstrap-2.5.0.min.js",
                        "~/Scripts/angular/angular-ui/ui-bootstrap-tpls-2.5.0.min.js"));

            bundles.Add(new Bundle("~/bundles/angular-addons1").Include(
                       "~/Scripts/angular/angular-loadscript.js",
                       "~/Scripts/angular/angular-cookies.min.js",
                       "~/Scripts/angular/common.js",
                       "~/Scripts/angular/ngToast.js",
                       "~/Scripts/angular/angular-resize.js"));

            bundles.Add(new Bundle("~/bundles/angular-addons2").Include(
                       "~/Scripts/angular/contextMenu.js",
                       "~/Scripts/angular/dirPagination.js",
                       "~/Scripts/angular/datetimepicker.js",
                       "~/Scripts/angular/dateTimeInput.js",
                       "~/Scripts/angular/ng-slide-down.js",
                       "~/Scripts/angular/bootstrap-colorpicker-module.js",
                       "~/Scripts/angular/angular-wysiwyg.js",
                       "~/Scripts/angular/angular-breadcrumb.min.js"));

            bundles.Add(new Bundle("~/bundles/angular-addons3").Include(
                       "~/Scripts/angular/angular-timer-all.min.js"));

            bundles.Add(new Bundle("~/bundles/FusionChart").Include(
                       "~/Scripts/fusion/FusionCharts.js",
                       //"~/Scripts/fusioncharts/js/themes/fusioncharts.theme.ocean.js",
                       //"~/Scripts/fusioncharts/js/themes/fusioncharts.theme.fint.js",
                       "~/Scripts/angular/angular-fusioncharts.min.js"));


            /*========== VISITOR ANGULAR ===========*/
            bundles.Add(new Bundle("~/bundles/VisitorApp").Include(
                 "~/App/app.js",
                 "~/App/Controllers/VisitorController.js",
                 "~/App/Services/VisitorWebAPIService.js"));

            /*========== ACCOUNT BUNDLE ==========*/
            bundles.Add(new Bundle("~/bundles/AccountApp").Include(
                "~/App/app.js",
                "~/App/Controllers/AccountController.js",
                "~/App/Services/AccountWebAPIService.js"));

            /*========== ADMIN ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/AdminApp").Include(
                   "~/Areas/Admin/App/app.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/AdminHomeApp").Include(
                   "~/Areas/Admin/App/Services/AdminHomeWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminHomeController.js"));

            bundles.Add(new Bundle("~/bundles/AdminBillingApp").Include(
                   "~/Areas/Admin/App/Services/AdminBillingWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminBillingController.js"));

            bundles.Add(new Bundle("~/bundles/AdminClassApp").Include(
                   "~/Areas/Admin/App/Services/AdminClassWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminClassController.js"));

            bundles.Add(new Bundle("~/bundles/AdminExtraCurricularApp").Include(
                   "~/Areas/Admin/App/Services/AdminExtraCurricularWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminExtraCurricularController.js"));

            bundles.Add(new Bundle("~/bundles/AdminFeeApp").Include(
                   "~/Areas/Admin/App/Services/AdminFeeWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminFeeController.js"));

            bundles.Add(new Bundle("~/bundles/AdminFinanceApp").Include(
                   "~/Areas/Admin/App/Services/AdminFinanceWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminFinanceController.js"));

            bundles.Add(new Bundle("~/bundles/AdminGradeApp").Include(
                   "~/Areas/Admin/App/Services/AdminGradeWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminGradeController.js"));

            bundles.Add(new Bundle("~/bundles/AdminMailApp").Include(
                   "~/Areas/Admin/App/Services/AdminMailWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminMailController.js"));

            bundles.Add(new Bundle("~/bundles/AdminParentApp").Include(
                   "~/Areas/Admin/App/Services/AdminParentWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminParentController.js"));

            bundles.Add(new Bundle("~/bundles/AdminProfileApp").Include(
                   "~/Areas/Admin/App/Services/AdminProfileWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminProfileController.js"));

            bundles.Add(new Bundle("~/bundles/AdminReportCardApp").Include(
                   "~/Areas/Admin/App/Services/AdminReportCardWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminReportCardController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSchoolApp").Include(
                   "~/Areas/Admin/App/Services/AdminSchoolWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSchoolController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSchoolSetupApp").Include(
                   "~/Areas/Admin/App/Services/AdminSchoolSetupWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSchoolSetupController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSessionApp").Include(
                   "~/Areas/Admin/App/Services/AdminSessionWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSessionController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSessionSetupApp").Include(
                   "~/Areas/Admin/App/Services/AdminSessionSetupWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSessionSetupController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSMSApp").Include(
                   "~/Areas/Admin/App/Services/AdminSMSWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSMSController.js"));

            bundles.Add(new Bundle("~/bundles/AdminStaffApp").Include(
                   "~/Areas/Admin/App/Services/AdminStaffWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminStaffController.js"));

            bundles.Add(new Bundle("~/bundles/AdminStudentApp").Include(
                   "~/Areas/Admin/App/Services/AdminStudentWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminStudentController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSubjectApp").Include(
                   "~/Scripts/angular/datetime-picker.js",
                   "~/Areas/Admin/App/Services/AdminSubjectWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSubjectController.js"));

            bundles.Add(new Bundle("~/bundles/AdminSuperAdminApp").Include(
                   "~/Areas/Admin/App/Services/AdminSuperAdminWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminSuperAdminController.js"));

            bundles.Add(new Bundle("~/bundles/AdminTestTypeApp").Include(
                   "~/Areas/Admin/App/Services/AdminTestTypeWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminTestTypeController.js"));

            bundles.Add(new Bundle("~/bundles/AdminTimetableApp").Include(
                   "~/Areas/Admin/App/Services/AdminTimetableWebAPIService.js",
                   "~/Areas/Admin/App/Controllers/AdminTimetableController.js"));


            /*========== PROPRIETOR ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/ProprietorApp").Include(
                   "~/Areas/proprietor/App/app.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/ProprietorProfileApp").Include(
                   "~/Areas/proprietor/App/Services/ProprietorProfileWebAPIService.js",
                   "~/Areas/proprietor/App/Controllers/ProprietorProfileController.js"));

            /*========== GROUP ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/GroupApp").Include(
                   "~/Areas/Group/App/app.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/GroupHomeApp").Include(
                   "~/Areas/Group/App/Services/GroupHomeWebAPIService.js",
                   "~/Areas/Group/App/Controllers/GroupHomeController.js"));

            bundles.Add(new Bundle("~/bundles/GroupAdministratorApp").Include(
                   "~/Areas/Group/App/Services/GroupAdministratorWebAPIService.js",
                   "~/Areas/Group/App/Controllers/GroupAdministratorController.js"));

            bundles.Add(new Bundle("~/bundles/GroupProfileApp").Include(
                   "~/Areas/Group/App/Services/GroupProfileWebAPIService.js",
                   "~/Areas/Group/App/Controllers/GroupProfileController.js"));

            bundles.Add(new Bundle("~/bundles/GroupSchoolApp").Include(
                   "~/Areas/Group/App/Services/GroupSchoolWebAPIService.js",
                   "~/Areas/Group/App/Controllers/GroupSchoolController.js"));

            /*========== TEACHER ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/TeacherApp").Include(
                   "~/Areas/Teacher/App/app.js",
                   "~/Areas/Teacher/App/Services/TeacherSharedService.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/TeacherHomeApp").Include(
                   "~/Areas/Teacher/App/Services/TeacherHomeWebAPIService.js",
                   "~/Areas/Teacher/App/Controllers/TeacherHomeController.js"));

            bundles.Add(new Bundle("~/bundles/TeacherProfileApp").Include(
                   "~/Areas/Teacher/App/Services/TeacherProfileWebAPIService.js",
                   "~/Areas/Teacher/App/Controllers/TeacherProfileController.js"));

            bundles.Add(new Bundle("~/bundles/TeacherClassArmApp").Include(
                   "~/Areas/Teacher/App/Services/TeacherClassArmWebAPIService.js",
                   "~/Areas/Teacher/App/Controllers/TeacherClassArmController.js"));

            bundles.Add(new Bundle("~/bundles/TeacherSubjectApp").Include(
                   "~/Scripts/angular/datetime-picker.js",
                   "~/Areas/Teacher/App/Services/TeacherSubjectWebAPIService.js",
                   "~/Areas/Teacher/App/Controllers/TeacherSubjectController.js"));

            bundles.Add(new Bundle("~/bundles/TeacherTimetableApp").Include(
                   "~/Areas/Teacher/App/Services/TeacherTimetableWebAPIService.js",
                   "~/Areas/Teacher/App/Controllers/TeacherTimetableController.js"));

            /*========== PARENT ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/ParentApp").Include(
                   "~/Areas/Parent/App/app.js",
                   "~/Areas/Parent/App/Services/ParentSharedService.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/ParentHomeApp").Include(
                   "~/Areas/Parent/App/Services/ParentHomeWebAPIService.js",
                   "~/Areas/Parent/App/Controllers/ParentHomeController.js"));

            bundles.Add(new Bundle("~/bundles/ParentProfileApp").Include(
                   "~/Areas/Parent/App/Services/ParentProfileWebAPIService.js",
                   "~/Areas/Parent/App/Controllers/ParentProfileController.js"));

            bundles.Add(new Bundle("~/bundles/ParentChildrenApp").Include(
                   "~/Areas/Parent/App/Services/ParentChildrenWebAPIService.js",
                   "~/Areas/Parent/App/Controllers/ParentChildrenController.js"));

            /*========== STUDENT ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/StudentApp").Include(
                   "~/Areas/Student/App/app.js",
                   "~/Areas/Student/App/Services/StudentSharedService.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/StudentHomeApp").Include(
                   "~/Areas/Student/App/Services/StudentHomeWebAPIService.js",
                   "~/Areas/Student/App/Controllers/StudentHomeController.js"));

            bundles.Add(new Bundle("~/bundles/StudentProfileApp").Include(
                   "~/Areas/Student/App/Services/StudentProfileWebAPIService.js",
                   "~/Areas/Student/App/Controllers/StudentProfileController.js"));

            bundles.Add(new Bundle("~/bundles/StudentResultApp").Include(
                   "~/Areas/Student/App/Services/StudentResultWebAPIService.js",
                   "~/Areas/Student/App/Controllers/StudentResultController.js"));

            bundles.Add(new Bundle("~/bundles/StudentSubjectApp").Include(
                   "~/Areas/Student/App/Services/StudentSubjectWebAPIService.js",
                   "~/Areas/Student/App/Controllers/StudentSubjectController.js"));

            bundles.Add(new Bundle("~/bundles/StudentTimetableApp").Include(
                   "~/Areas/Student/App/Services/StudentTimetableWebAPIService.js",
                   "~/Areas/Student/App/Controllers/StudentTimetableController.js"));

            /*========== STAFF ANGULAR ==========*/
            bundles.Add(new Bundle("~/bundles/StaffApp").Include(
                   "~/Areas/Staff/App/app.js",
                   "~/Areas/Staff/App/Services/StaffSharedService.js",
                   "~/Areas/Admin/App/Directives/AdminDirectives.js",
                   "~/Areas/Admin/App/Services/AdminSharedService.js",
                   "~/Areas/Admin/App/Controllers/AdminSharedControllers.js",
                   "~/Areas/Admin/App/Filters/AdminFilters.js"));

            bundles.Add(new Bundle("~/bundles/StaffHomeApp").Include(
                   "~/Areas/Staff/App/Services/StaffHomeWebAPIService.js",
                   "~/Areas/Staff/App/Controllers/StaffHomeController.js"));

            bundles.Add(new Bundle("~/bundles/StaffProfileApp").Include(
                   "~/Areas/Staff/App/Services/StaffProfileWebAPIService.js",
                   "~/Areas/Staff/App/Controllers/StaffProfileController.js"));

            // BundleTable.EnableOptimizations = false;

        }
    }
}
